# 故障排除指南

## 🚨 当前问题：控制面板显示"正在搜索视频"但没有播放

### 📋 问题诊断步骤

#### 1. 检查插件状态
1. 刷新插件：访问 `chrome://extensions/` → 找到插件 → 点击刷新按钮
2. 检查错误：查看插件是否有错误信息

#### 2. 使用调试工具
1. 打开调试页面：
   - 在地址栏输入：`chrome-extension://[插件ID]/debug.html`
   - 或者创建新标签页，输入插件的调试页面URL

2. 测试搜索功能：
   - 点击"打开B站搜索页面"
   - 点击"测试视频选择器"
   - 查看控制台输出

#### 3. 手动测试
1. **打开B站搜索页面**：
   ```
   https://search.bilibili.com/all?keyword=mcp技术
   ```

2. **打开浏览器控制台**（F12）

3. **运行测试脚本**：
   ```javascript
   // 测试视频选择器
   const selectors = [
     '.video-item .bili-video-card__info--tit a',
     '.bili-video-card .bili-video-card__info--tit a',
     '.video-item .title a',
     'a[href*="/video/BV"]',
     'a[href*="/video/av"]'
   ];

   selectors.forEach(selector => {
     const elements = document.querySelectorAll(selector);
     console.log(`${selector}: ${elements.length} 个元素`);
     if (elements.length > 0) {
       console.log('第一个元素:', elements[0].textContent.trim(), elements[0].href);
     }
   });
   ```

### 🔧 可能的解决方案

#### 方案1：B站页面结构变化
如果测试脚本显示找不到视频元素，说明B站更新了页面结构：

1. **查找新的选择器**：
   ```javascript
   // 在B站搜索页面控制台运行
   const allLinks = document.querySelectorAll('a[href*="/video/"]');
   console.log('找到视频链接:', allLinks.length);
   
   // 查看前几个链接的父元素结构
   for (let i = 0; i < Math.min(3, allLinks.length); i++) {
     console.log(`链接 ${i+1}:`, allLinks[i].outerHTML);
     console.log('父元素:', allLinks[i].parentElement.outerHTML);
   }
   ```

2. **更新选择器**：根据找到的结构更新 `background.js` 中的 `videoSelectors` 数组

#### 方案2：页面加载时间问题
如果页面加载较慢：

1. 增加等待时间（已在代码中增加到8秒）
2. 检查网络连接
3. 尝试手动等待页面完全加载后再启动

#### 方案3：权限问题
检查插件权限：

1. 确认 `manifest.json` 中有正确的权限
2. 检查 `host_permissions` 是否包含 B站域名
3. 重新安装插件

### 🐛 调试技巧

#### 1. 查看控制台日志
- 打开浏览器控制台（F12）
- 查看 Console 标签页
- 寻找插件相关的日志信息

#### 2. 检查网络请求
- 在控制台的 Network 标签页查看请求
- 确认B站页面正常加载

#### 3. 使用调试面板
- 在B站页面使用调试工具的"注入调试脚本"功能
- 查看页面右上角的调试面板信息

### 📝 常见错误信息

#### "未找到任何视频元素"
- **原因**：B站页面结构变化或页面未完全加载
- **解决**：更新视频选择器或增加等待时间

#### "注入脚本失败"
- **原因**：标签页权限问题或页面限制
- **解决**：检查插件权限，重新加载插件

#### "连接已断开"
- **原因**：background script出错或插件被禁用
- **解决**：刷新插件，检查错误信息

### 🔄 快速修复步骤

1. **刷新插件**：
   ```
   chrome://extensions/ → 找到插件 → 点击刷新
   ```

2. **清除缓存**：
   ```
   Ctrl+Shift+Delete → 清除浏览数据
   ```

3. **重新安装插件**：
   ```
   移除插件 → 重新加载插件文件夹
   ```

4. **检查B站页面**：
   ```
   手动访问 https://search.bilibili.com/all?keyword=test
   确认页面正常显示搜索结果
   ```

### 📞 获取帮助

如果以上方法都无法解决问题：

1. **收集信息**：
   - 浏览器版本
   - 插件版本
   - 错误信息截图
   - 控制台日志

2. **提供详细描述**：
   - 具体操作步骤
   - 预期结果 vs 实际结果
   - 是否在特定条件下发生

3. **测试环境**：
   - 尝试在隐私模式下测试
   - 禁用其他扩展后测试
   - 在不同网络环境下测试

### 🎯 预防措施

1. **定期更新**：关注B站页面结构变化
2. **备份配置**：保存工作的插件版本
3. **测试环境**：在测试环境中验证更新
4. **监控日志**：定期检查控制台错误信息
