<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>B站自动播放器插件测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .step {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .step h3 {
            margin-top: 0;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 B站视频自动播放器</h1>
        <p>Chrome插件测试和安装指南</p>
    </div>

    <div class="section">
        <h2>📋 项目完整性检查</h2>
        <ul class="checklist">
            <li>manifest.json - Chrome插件配置文件</li>
            <li>popup.html - 弹窗界面</li>
            <li>popup.js - 弹窗逻辑</li>
            <li>background.js - 后台服务脚本</li>
            <li>content.js - 内容脚本</li>
            <li>icons/ - 图标文件夹</li>
            <li>README.md - 说明文档</li>
        </ul>
    </div>

    <div class="section">
        <h2>🔧 安装步骤</h2>
        
        <div class="step">
            <h3>步骤 1: 生成图标文件</h3>
            <p>首先需要生成PNG格式的图标文件：</p>
            <div class="code">
                1. 打开 create_icons.html 文件<br>
                2. 右键点击每个图标，选择"图片另存为"<br>
                3. 保存为 icon16.png, icon32.png, icon48.png, icon128.png<br>
                4. 将PNG文件放入 icons/ 目录
            </div>
        </div>

        <div class="step">
            <h3>步骤 2: 加载插件到Chrome</h3>
            <div class="code">
                1. 打开Chrome浏览器<br>
                2. 访问 chrome://extensions/<br>
                3. 开启右上角的"开发者模式"<br>
                4. 点击"加载已解压的扩展程序"<br>
                5. 选择插件文件夹
            </div>
        </div>

        <div class="step">
            <h3>步骤 3: 测试插件功能</h3>
            <div class="code">
                1. 点击浏览器工具栏中的插件图标<br>
                2. 设置搜索关键词（默认：mcp技术）<br>
                3. 设置播放时长（默认：30秒）<br>
                4. 设置最大播放数量（默认：5个）<br>
                5. 点击"开始播放"按钮
            </div>
        </div>
    </div>

    <div class="warning">
        <strong>⚠️ 注意事项：</strong><br>
        • 确保网络连接稳定，能正常访问B站<br>
        • 插件需要访问B站页面的权限<br>
        • 某些视频可能因版权或其他原因无法播放<br>
        • 长时间运行可能占用较多系统资源
    </div>

    <div class="section">
        <h2>🧪 功能测试清单</h2>
        <ul class="checklist">
            <li>插件图标正常显示</li>
            <li>弹窗界面正常打开</li>
            <li>参数设置功能正常</li>
            <li>自动打开B站搜索页面</li>
            <li>成功收集视频链接</li>
            <li>依次播放视频</li>
            <li>播放时长控制准确</li>
            <li>播放状态实时更新</li>
            <li>停止功能正常工作</li>
            <li>播放记录正确显示</li>
        </ul>
    </div>

    <div class="section">
        <h2>🐛 常见问题排查</h2>
        
        <div class="step">
            <h3>问题：插件无法加载</h3>
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>检查是否开启开发者模式</li>
                <li>确认所有文件完整，特别是图标文件</li>
                <li>查看Chrome扩展页面的错误信息</li>
                <li>检查manifest.json语法是否正确</li>
            </ul>
        </div>

        <div class="step">
            <h3>问题：搜索无结果</h3>
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>检查网络连接</li>
                <li>尝试更换搜索关键词</li>
                <li>确认B站页面正常加载</li>
                <li>检查页面选择器是否匹配当前B站结构</li>
            </ul>
        </div>

        <div class="step">
            <h3>问题：视频无法播放</h3>
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>检查视频是否需要登录</li>
                <li>尝试手动播放测试</li>
                <li>查看浏览器控制台错误</li>
                <li>检查播放按钮选择器是否正确</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>📊 技术架构</h2>
        <p>插件采用Chrome Manifest V3架构：</p>
        <ul>
            <li><strong>Popup UI:</strong> 用户界面交互</li>
            <li><strong>Background Script:</strong> 服务协调管理</li>
            <li><strong>Content Script:</strong> 页面内容操作</li>
            <li><strong>消息传递:</strong> 组件间通信</li>
        </ul>
    </div>

    <div class="section">
        <h2>🎯 核心功能</h2>
        <ul class="checklist">
            <li>自动搜索B站视频</li>
            <li>智能播放控制</li>
            <li>时长精确控制</li>
            <li>播放状态记录</li>
            <li>参数灵活配置</li>
            <li>实时进度显示</li>
            <li>防反爬策略</li>
        </ul>
    </div>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('B站自动播放器插件测试页面已加载');
            
            // 检查是否在Chrome扩展环境中
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                console.log('检测到Chrome扩展环境');
            } else {
                console.log('当前不在Chrome扩展环境中');
            }
        });
    </script>
</body>
</html>
