<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>B站自动播放器 - 调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #45a049;
        }
        .button.danger {
            background: #f44336;
        }
        .button.danger:hover {
            background: #da190b;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 B站自动播放器调试工具</h1>
        <p>用于测试和调试插件功能</p>
    </div>

    <div class="grid">
        <div class="section">
            <h3>🎮 控制面板</h3>
            <div class="input-group">
                <label for="searchKeyword">搜索关键词:</label>
                <input type="text" id="searchKeyword" value="mcp技术">
            </div>
            <div class="input-group">
                <label for="playDuration">播放时长(秒):</label>
                <input type="number" id="playDuration" value="10" min="5" max="60">
            </div>
            <div class="input-group">
                <label for="maxVideos">最大数量:</label>
                <input type="number" id="maxVideos" value="3" min="1" max="10">
            </div>
            <button class="button" onclick="startTest()">开始测试</button>
            <button class="button danger" onclick="stopTest()">停止测试</button>
            <button class="button" onclick="getStatus()">获取状态</button>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h3>📊 状态信息</h3>
            <div id="statusInfo" class="status info">
                点击"获取状态"查看当前状态
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📝 实时日志</h3>
        <div id="logOutput" class="log">等待操作...</div>
    </div>

    <div class="section">
        <h3>🔍 B站页面测试</h3>
        <button class="button" onclick="openBilibiliSearch()">打开B站搜索页面</button>
        <button class="button" onclick="testVideoSelectors()">测试视频选择器</button>
        <button class="button" onclick="injectDebugScript()">注入调试脚本</button>
    </div>

    <script>
        let logElement = document.getElementById('logOutput');
        let statusElement = document.getElementById('statusInfo');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            logElement.textContent = '';
        }

        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        async function startTest() {
            const config = {
                searchKeyword: document.getElementById('searchKeyword').value,
                playDuration: parseInt(document.getElementById('playDuration').value),
                maxVideos: parseInt(document.getElementById('maxVideos').value)
            };

            log('开始测试，配置: ' + JSON.stringify(config));
            updateStatus('正在启动测试...', 'info');

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'startAutoPlay',
                    config: config
                });

                if (response.success) {
                    log('✓ 测试启动成功');
                    updateStatus('测试已启动', 'success');
                } else {
                    log('✗ 测试启动失败: ' + response.error);
                    updateStatus('测试启动失败', 'error');
                }
            } catch (error) {
                log('✗ 发送消息失败: ' + error.message);
                updateStatus('通信错误', 'error');
            }
        }

        async function stopTest() {
            log('停止测试');
            updateStatus('正在停止测试...', 'info');

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'stopAutoPlay'
                });

                if (response.success) {
                    log('✓ 测试已停止');
                    updateStatus('测试已停止', 'success');
                } else {
                    log('✗ 停止失败: ' + response.error);
                    updateStatus('停止失败', 'error');
                }
            } catch (error) {
                log('✗ 发送消息失败: ' + error.message);
                updateStatus('通信错误', 'error');
            }
        }

        async function getStatus() {
            log('获取状态信息');

            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'getStatus'
                });

                if (response.status) {
                    const status = response.status;
                    log('状态信息: ' + JSON.stringify(status, null, 2));
                    updateStatus(`运行中: ${status.isRunning}, 视频: ${status.progress.current}/${status.progress.total}`, 'info');
                } else {
                    log('✗ 获取状态失败');
                    updateStatus('获取状态失败', 'error');
                }
            } catch (error) {
                log('✗ 发送消息失败: ' + error.message);
                updateStatus('通信错误', 'error');
            }
        }

        async function openBilibiliSearch() {
            const keyword = document.getElementById('searchKeyword').value;
            const url = `https://search.bilibili.com/all?keyword=${encodeURIComponent(keyword)}`;
            
            log('打开B站搜索页面: ' + url);
            
            try {
                const tab = await chrome.tabs.create({ url: url, active: true });
                log('✓ 页面已打开，标签页ID: ' + tab.id);
                updateStatus('B站搜索页面已打开', 'success');
            } catch (error) {
                log('✗ 打开页面失败: ' + error.message);
                updateStatus('打开页面失败', 'error');
            }
        }

        async function testVideoSelectors() {
            log('测试视频选择器...');
            
            try {
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (tabs.length === 0) {
                    log('✗ 没有找到活动标签页');
                    return;
                }

                const tabId = tabs[0].id;
                log('在标签页 ' + tabId + ' 中测试选择器');

                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: () => {
                        console.log('=== 视频选择器测试 ===');
                        console.log('页面URL:', window.location.href);
                        
                        const selectors = [
                            '.video-item .bili-video-card__info--tit a',
                            '.bili-video-card .bili-video-card__info--tit a',
                            '.video-item .title a',
                            'a[href*="/video/BV"]',
                            'a[href*="/video/av"]'
                        ];

                        selectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            console.log(`选择器 "${selector}": ${elements.length} 个元素`);
                            
                            if (elements.length > 0) {
                                console.log('前3个元素:');
                                for (let i = 0; i < Math.min(3, elements.length); i++) {
                                    console.log(`  ${i+1}. ${elements[i].textContent.trim().substring(0, 50)} - ${elements[i].href}`);
                                }
                            }
                        });
                    }
                });

                log('✓ 选择器测试完成，请查看控制台');
                updateStatus('选择器测试完成', 'success');
            } catch (error) {
                log('✗ 测试失败: ' + error.message);
                updateStatus('测试失败', 'error');
            }
        }

        async function injectDebugScript() {
            log('注入调试脚本...');
            
            try {
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (tabs.length === 0) {
                    log('✗ 没有找到活动标签页');
                    return;
                }

                const tabId = tabs[0].id;
                
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: () => {
                        // 创建调试面板
                        const debugPanel = document.createElement('div');
                        debugPanel.id = 'bilibili-debug-panel';
                        debugPanel.style.cssText = `
                            position: fixed;
                            top: 10px;
                            right: 10px;
                            width: 300px;
                            background: white;
                            border: 2px solid #4CAF50;
                            border-radius: 8px;
                            padding: 15px;
                            z-index: 10000;
                            font-family: Arial, sans-serif;
                            font-size: 12px;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                        `;
                        
                        debugPanel.innerHTML = `
                            <h3 style="margin: 0 0 10px 0; color: #4CAF50;">🔧 调试面板</h3>
                            <div id="debug-info"></div>
                            <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                        `;
                        
                        document.body.appendChild(debugPanel);
                        
                        // 更新调试信息
                        function updateDebugInfo() {
                            const info = document.getElementById('debug-info');
                            if (!info) return;
                            
                            const videoLinks = document.querySelectorAll('a[href*="/video/"]');
                            const bvLinks = document.querySelectorAll('a[href*="/video/BV"]');
                            
                            info.innerHTML = `
                                <div><strong>页面信息:</strong></div>
                                <div>URL: ${window.location.href}</div>
                                <div>标题: ${document.title}</div>
                                <div>加载状态: ${document.readyState}</div>
                                <div><strong>视频链接:</strong></div>
                                <div>总video链接: ${videoLinks.length}</div>
                                <div>BV链接: ${bvLinks.length}</div>
                                <div><strong>页面元素:</strong></div>
                                <div>总链接数: ${document.querySelectorAll('a').length}</div>
                                <div>总div数: ${document.querySelectorAll('div').length}</div>
                            `;
                        }
                        
                        updateDebugInfo();
                        setInterval(updateDebugInfo, 2000);
                        
                        console.log('调试面板已注入');
                    }
                });

                log('✓ 调试脚本注入成功');
                updateStatus('调试脚本已注入', 'success');
            } catch (error) {
                log('✗ 注入失败: ' + error.message);
                updateStatus('注入失败', 'error');
            }
        }

        // 定期更新状态
        setInterval(async () => {
            try {
                const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
                if (response && response.status && response.status.isRunning) {
                    const status = response.status;
                    updateStatus(`运行中: ${status.progress.current}/${status.progress.total}`, 'info');
                }
            } catch (error) {
                // 忽略错误
            }
        }, 2000);

        log('调试工具已加载');
    </script>
</body>
</html>
