<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>B站自动播放器 - 调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #45a049;
        }
        .button.danger {
            background: #f44336;
        }
        .button.danger:hover {
            background: #da190b;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 B站自动播放器调试工具</h1>
        <p>用于测试和调试插件功能</p>
    </div>

    <div class="grid">
        <div class="section">
            <h3>🎮 控制面板</h3>
            <div class="input-group">
                <label for="searchKeyword">搜索关键词:</label>
                <input type="text" id="searchKeyword" value="mcp技术">
            </div>
            <div class="input-group">
                <label for="playDuration">播放时长(秒):</label>
                <input type="number" id="playDuration" value="10" min="5" max="60">
            </div>
            <div class="input-group">
                <label for="maxVideos">最大数量:</label>
                <input type="number" id="maxVideos" value="3" min="1" max="10">
            </div>
            <button class="button" id="startTestBtn">开始测试</button>
            <button class="button danger" id="stopTestBtn">停止测试</button>
            <button class="button" id="getStatusBtn">获取状态</button>
            <button class="button" id="clearLogBtn">清空日志</button>
        </div>

        <div class="section">
            <h3>📊 状态信息</h3>
            <div id="statusInfo" class="status info">
                点击"获取状态"查看当前状态
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📝 实时日志</h3>
        <div id="logOutput" class="log">等待操作...</div>
    </div>

    <div class="section">
        <h3>🔍 B站页面测试</h3>
        <button class="button" id="openBilibiliBtn">打开B站搜索页面</button>
        <button class="button" id="testSelectorsBtn">测试视频选择器</button>
        <button class="button" id="injectDebugBtn">注入调试脚本</button>
        <button class="button" id="quickTestBtn">快速测试搜索功能</button>
    </div>

    <script src="debug.js"></script>
</body>
</html>
