// control.js - 控制窗口脚本
class ControlWindow {
  constructor() {
    this.elements = {
      searchKeyword: document.getElementById('searchKeyword'),
      playDuration: document.getElementById('playDuration'),
      maxVideos: document.getElementById('maxVideos'),
      status: document.getElementById('status'),
      progressFill: document.getElementById('progressFill'),
      progressText: document.getElementById('progressText'),
      videoList: document.getElementById('videoList'),
      stopBtn: document.getElementById('stopBtn')
    };
    
    this.init();
  }
  
  init() {
    console.log('控制窗口已初始化');
    
    // 绑定停止按钮事件
    this.elements.stopBtn.addEventListener('click', () => {
      this.stopAutoPlay();
    });
    
    // 开始状态更新
    this.startStatusUpdate();
    
    // 获取初始配置
    this.loadConfig();
  }
  
  async loadConfig() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'getStatus'
      });
      
      if (response && response.status) {
        // 显示配置信息（从storage获取）
        const config = await chrome.storage.local.get([
          'searchKeyword', 'playDuration', 'maxVideos'
        ]);
        
        this.elements.searchKeyword.textContent = config.searchKeyword || 'mcp技术';
        this.elements.playDuration.textContent = config.playDuration || '30';
        this.elements.maxVideos.textContent = config.maxVideos || '5';
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  }
  
  async stopAutoPlay() {
    try {
      await chrome.runtime.sendMessage({
        action: 'stopAutoPlay'
      });
      
      // 关闭当前窗口
      window.close();
    } catch (error) {
      console.error('停止播放失败:', error);
    }
  }
  
  startStatusUpdate() {
    // 每秒更新状态
    setInterval(async () => {
      try {
        const response = await chrome.runtime.sendMessage({
          action: 'getStatus'
        });
        
        if (response && response.status) {
          this.updateStatus(response.status);
        }
      } catch (error) {
        // 如果background script不可用，关闭窗口
        console.error('无法连接到background script:', error);
        this.elements.status.textContent = '连接已断开';
      }
    }, 1000);
  }
  
  updateStatus(status) {
    const { isRunning, currentVideo, videoList, progress } = status;
    
    // 更新状态文本
    if (currentVideo) {
      this.elements.status.textContent = `正在播放: ${currentVideo.title}`;
    } else if (isRunning) {
      this.elements.status.textContent = '正在搜索视频...';
    } else {
      this.elements.status.textContent = '播放已完成';
    }
    
    // 更新进度条
    if (progress && progress.total > 0) {
      const percentage = (progress.current / progress.total) * 100;
      this.elements.progressFill.style.width = `${percentage}%`;
      this.elements.progressText.textContent = `${progress.current} / ${progress.total}`;
    } else {
      this.elements.progressFill.style.width = '0%';
      this.elements.progressText.textContent = '0 / 0';
    }
    
    // 更新视频列表
    if (videoList && videoList.length > 0) {
      this.updateVideoList(videoList);
    }
  }
  
  updateVideoList(videos) {
    this.elements.videoList.innerHTML = '';
    
    videos.forEach((video, index) => {
      const item = document.createElement('div');
      item.className = `video-item ${video.status}`;
      
      const title = document.createElement('div');
      title.className = 'video-title';
      title.textContent = video.title;
      
      const info = document.createElement('div');
      info.className = 'video-info';
      info.textContent = `播放时长: ${video.playedDuration || 0}s | 状态: ${this.getStatusText(video.status)}`;
      
      item.appendChild(title);
      item.appendChild(info);
      this.elements.videoList.appendChild(item);
    });
  }
  
  getStatusText(status) {
    const statusMap = {
      'pending': '等待中',
      'playing': '播放中',
      'completed': '已完成',
      'error': '错误'
    };
    return statusMap[status] || status;
  }
}

// 初始化控制窗口
document.addEventListener('DOMContentLoaded', () => {
  new ControlWindow();
});

// 防止窗口意外关闭
window.addEventListener('beforeunload', (e) => {
  e.preventDefault();
  e.returnValue = '确定要关闭控制面板吗？这将停止自动播放。';
});
