// 创建简单的PNG图标文件的Node.js脚本
const fs = require('fs');

// 创建一个简单的PNG文件（1x1像素的透明图片）
function createMinimalPNG() {
  // 最小的PNG文件数据（1x1透明像素）
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // width: 1
    0x00, 0x00, 0x00, 0x01, // height: 1
    0x08, 0x06, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0x1F, 0x15, 0xC4, 0x89, // CRC
    0x00, 0x00, 0x00, 0x0A, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // compressed data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  return pngData;
}

// 创建所有尺寸的图标
const sizes = [16, 32, 48, 128];
const pngData = createMinimalPNG();

sizes.forEach(size => {
  const filename = `icons/icon${size}.png`;
  fs.writeFileSync(filename, pngData);
  console.log(`已创建临时图标: ${filename}`);
});

console.log('\n临时PNG图标创建完成！');
console.log('插件现在可以正常加载了。');
console.log('建议稍后使用 create_icons.html 生成更好看的图标。');
