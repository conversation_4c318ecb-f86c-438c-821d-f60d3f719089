// popup.js - 弹窗界面逻辑
class PopupController {
  constructor() {
    this.isRunning = false;
    this.currentVideoIndex = 0;
    this.videoList = [];
    this.initializeElements();
    this.bindEvents();
    this.loadSettings();
    this.updateStatus();
  }

  initializeElements() {
    this.elements = {
      searchKeyword: document.getElementById('searchKeyword'),
      playDuration: document.getElementById('playDuration'),
      maxVideos: document.getElementById('maxVideos'),
      startBtn: document.getElementById('startBtn'),
      stopBtn: document.getElementById('stopBtn'),
      status: document.getElementById('status'),
      progressFill: document.getElementById('progressFill'),
      videoList: document.getElementById('videoList'),
      keepOpenNotice: document.getElementById('keepOpenNotice')
    };
  }

  bindEvents() {
    this.elements.startBtn.addEventListener('click', () => this.startAutoPlay());
    this.elements.stopBtn.addEventListener('click', () => this.stopAutoPlay());
    
    // 保存设置
    ['searchKeyword', 'playDuration', 'maxVideos'].forEach(id => {
      this.elements[id].addEventListener('change', () => this.saveSettings());
    });
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'searchKeyword', 'playDuration', 'maxVideos'
      ]);
      
      if (result.searchKeyword) this.elements.searchKeyword.value = result.searchKeyword;
      if (result.playDuration) this.elements.playDuration.value = result.playDuration;
      if (result.maxVideos) this.elements.maxVideos.value = result.maxVideos;
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }

  async saveSettings() {
    try {
      await chrome.storage.local.set({
        searchKeyword: this.elements.searchKeyword.value,
        playDuration: parseInt(this.elements.playDuration.value),
        maxVideos: parseInt(this.elements.maxVideos.value)
      });
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  }

  async startAutoPlay() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.elements.startBtn.disabled = true;
    this.elements.stopBtn.disabled = false;

    const config = {
      searchKeyword: this.elements.searchKeyword.value.trim(),
      playDuration: parseInt(this.elements.playDuration.value),
      maxVideos: parseInt(this.elements.maxVideos.value)
    };

    if (!config.searchKeyword) {
      this.showStatus('请输入搜索关键词', 'error');
      this.stopAutoPlay();
      return;
    }

    try {
      this.showStatus('正在启动自动播放...', 'info');

      // 发送消息给background script开始自动播放
      await chrome.runtime.sendMessage({
        action: 'startAutoPlay',
        config: config
      });

      this.showStatus('自动播放已启动', 'success');

      // 显示保持打开提示
      this.elements.keepOpenNotice.style.display = 'block';

      // 保持popup窗口打开
      this.keepPopupOpen();

    } catch (error) {
      console.error('启动失败:', error);
      this.showStatus('启动失败: ' + error.message, 'error');
      this.stopAutoPlay();
    }
  }

  // 保持popup窗口打开的方法
  keepPopupOpen() {
    // 通过定期与background通信来保持popup活跃
    this.keepAliveInterval = setInterval(async () => {
      try {
        await chrome.runtime.sendMessage({ action: 'keepAlive' });
      } catch (error) {
        // 忽略连接错误
      }
    }, 1000);

    // 防止popup因为失去焦点而关闭
    window.addEventListener('blur', (e) => {
      if (this.isRunning) {
        e.preventDefault();
        setTimeout(() => {
          window.focus();
        }, 100);
      }
    });

    // 保持窗口活跃状态
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isRunning) {
        setTimeout(() => {
          window.focus();
        }, 100);
      }
    });
  }

  async stopAutoPlay() {
    this.isRunning = false;
    this.elements.startBtn.disabled = false;
    this.elements.stopBtn.disabled = true;

    // 隐藏保持打开提示
    this.elements.keepOpenNotice.style.display = 'none';

    // 清理保持打开的定时器
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
      this.keepAliveInterval = null;
    }

    try {
      await chrome.runtime.sendMessage({
        action: 'stopAutoPlay'
      });
      this.showStatus('已停止自动播放', 'info');
    } catch (error) {
      console.error('停止失败:', error);
    }
  }

  showStatus(message, type = 'info') {
    this.elements.status.textContent = message;
    this.elements.status.className = `status ${type}`;
  }

  updateProgress(current, total) {
    const percentage = total > 0 ? (current / total) * 100 : 0;
    this.elements.progressFill.style.width = `${percentage}%`;
  }

  updateVideoList(videos) {
    this.videoList = videos;
    this.renderVideoList();
  }

  renderVideoList() {
    this.elements.videoList.innerHTML = '';
    
    this.videoList.forEach((video, index) => {
      const item = document.createElement('div');
      item.className = `video-item ${video.status}`;
      item.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 3px;">${video.title}</div>
        <div style="font-size: 11px; opacity: 0.8;">
          播放时长: ${video.playedDuration || 0}s | 状态: ${this.getStatusText(video.status)}
        </div>
      `;
      this.elements.videoList.appendChild(item);
    });
  }

  getStatusText(status) {
    const statusMap = {
      'pending': '等待中',
      'playing': '播放中',
      'completed': '已完成',
      'error': '错误'
    };
    return statusMap[status] || status;
  }

  async updateStatus() {
    // 定期更新状态
    setInterval(async () => {
      try {
        const response = await chrome.runtime.sendMessage({
          action: 'getStatus'
        });
        
        if (response && response.status) {
          const { isRunning, currentVideo, videoList, progress } = response.status;
          
          if (isRunning !== this.isRunning) {
            this.isRunning = isRunning;
            this.elements.startBtn.disabled = isRunning;
            this.elements.stopBtn.disabled = !isRunning;
          }
          
          if (currentVideo) {
            this.showStatus(`正在播放: ${currentVideo.title}`, 'info');
          }
          
          if (videoList) {
            this.updateVideoList(videoList);
          }
          
          if (progress) {
            this.updateProgress(progress.current, progress.total);
          }
        }
      } catch (error) {
        // 忽略连接错误
      }
    }, 1000);
  }
}

// 初始化弹窗控制器
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
