// 生成Chrome插件图标的Node.js脚本
const fs = require('fs');
const path = require('path');

// 创建SVG图标内容
function createSVGIcon(size) {
  const margin = Math.max(1, size / 10);
  const radius = (size - margin * 2) / 2;
  const centerX = size / 2;
  const centerY = size / 2;
  const triangleSize = size / 3;
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bg-gradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- 圆形背景 -->
  <circle cx="${centerX}" cy="${centerY}" r="${radius}" 
          fill="url(#bg-gradient)" 
          stroke="#4c64d6" 
          stroke-width="${Math.max(1, size / 32)}" />
  
  <!-- 播放按钮三角形 -->
  <polygon points="${centerX - triangleSize/3},${centerY - triangleSize/2} ${centerX - triangleSize/3},${centerY + triangleSize/2} ${centerX + triangleSize/2},${centerY}"
           fill="#ffffff" />
  
  ${size >= 32 ? `<!-- B站标识 -->
  <circle cx="${size - size/8 - margin}" cy="${margin + size/8}" r="${size/16}" fill="#fb7299" />` : ''}
</svg>`;
}

// 创建Base64编码的PNG数据（简化版）
function createBase64PNG(size) {
  // 这是一个简化的PNG生成，实际应用中建议使用专门的图像处理库
  const svgContent = createSVGIcon(size);
  
  // 将SVG转换为Base64（这里只是示例，实际需要SVG到PNG的转换）
  const base64SVG = Buffer.from(svgContent).toString('base64');
  
  return `data:image/svg+xml;base64,${base64SVG}`;
}

// 创建图标文件
function createIconFiles() {
  // 创建icons目录
  const iconsDir = path.join(__dirname, 'icons');
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
  }
  
  const sizes = [16, 32, 48, 128];
  
  sizes.forEach(size => {
    const svgContent = createSVGIcon(size);
    const svgPath = path.join(iconsDir, `icon${size}.svg`);
    
    fs.writeFileSync(svgPath, svgContent);
    console.log(`已创建: icon${size}.svg`);
  });
  
  // 创建一个HTML文件用于转换SVG到PNG
  const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <title>SVG to PNG Converter</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .converter { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>SVG to PNG Converter</h1>
    <p>点击按钮将SVG转换为PNG并下载</p>
    
    ${sizes.map(size => `
    <div class="converter">
        <h3>Icon ${size}x${size}</h3>
        <canvas id="canvas${size}" width="${size}" height="${size}"></canvas>
        <button onclick="convertAndDownload(${size})">下载 PNG</button>
    </div>
    `).join('')}
    
    <script>
        const svgContents = {
            ${sizes.map(size => `${size}: \`${createSVGIcon(size).replace(/`/g, '\\`')}\``).join(',\n            ')}
        };
        
        function convertAndDownload(size) {
            const canvas = document.getElementById('canvas' + size);
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                
                // 下载PNG
                const link = document.createElement('a');
                link.download = 'icon' + size + '.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            };
            
            const svgBlob = new Blob([svgContents[size]], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            img.src = url;
        }
        
        // 自动渲染预览
        Object.keys(svgContents).forEach(size => {
            const canvas = document.getElementById('canvas' + size);
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, parseInt(size), parseInt(size));
            };
            
            const svgBlob = new Blob([svgContents[size]], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            img.src = url;
        });
    </script>
</body>
</html>`;
  
  fs.writeFileSync(path.join(__dirname, 'svg_to_png.html'), htmlContent);
  console.log('已创建: svg_to_png.html (用于转换SVG到PNG)');
  
  console.log('\n图标生成完成！');
  console.log('1. SVG图标已保存在 icons/ 目录中');
  console.log('2. 打开 svg_to_png.html 文件在浏览器中转换为PNG格式');
  console.log('3. 或者使用在线SVG转PNG工具转换icons目录中的SVG文件');
}

// 执行图标创建
createIconFiles();
