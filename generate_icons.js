// 生成Chrome插件图标的简化脚本
const fs = require('fs');
const path = require('path');

// 创建SVG图标内容
function createSVGIcon(size) {
  const margin = Math.max(1, size / 10);
  const radius = (size - margin * 2) / 2;
  const centerX = size / 2;
  const centerY = size / 2;
  const triangleSize = size / 3;

  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bg-gradient-${size}" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </radialGradient>
  </defs>

  <!-- 圆形背景 -->
  <circle cx="${centerX}" cy="${centerY}" r="${radius}"
          fill="url(#bg-gradient-${size})"
          stroke="#4c64d6"
          stroke-width="${Math.max(1, size / 32)}" />

  <!-- 播放按钮三角形 -->
  <polygon points="${centerX - triangleSize/3},${centerY - triangleSize/2} ${centerX - triangleSize/3},${centerY + triangleSize/2} ${centerX + triangleSize/2},${centerY}"
           fill="#ffffff" />

  ${size >= 32 ? `<!-- B站标识 -->
  <circle cx="${size - size/8 - margin}" cy="${margin + size/8}" r="${size/16}" fill="#fb7299" />` : ''}
</svg>`;
}

// 创建图标文件
function createIconFiles() {
  // 创建icons目录
  const iconsDir = path.join(__dirname, 'icons');
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
  }

  const sizes = [16, 32, 48, 128];

  sizes.forEach(size => {
    const svgContent = createSVGIcon(size);
    const svgPath = path.join(iconsDir, `icon${size}.svg`);

    fs.writeFileSync(svgPath, svgContent);
    console.log(`已创建: icon${size}.svg`);
  });

  console.log('\n图标生成完成！');
  console.log('SVG图标已保存在 icons/ 目录中');
  console.log('请使用以下方法之一将SVG转换为PNG:');
  console.log('1. 打开 create_icons.html 文件在浏览器中转换');
  console.log('2. 使用在线SVG转PNG工具');
  console.log('3. 使用图像编辑软件如GIMP、Photoshop等');
}

// 执行图标创建
createIconFiles();
