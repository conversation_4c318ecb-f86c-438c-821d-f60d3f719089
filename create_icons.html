<!DOCTYPE html>
<html>
<head>
    <title>生成Chrome插件图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px; display: inline-block; }
        canvas { border: 1px solid #ccc; margin: 10px; }
    </style>
</head>
<body>
    <h1>Chrome插件图标生成器</h1>
    <p>右键点击图标保存为PNG文件</p>
    
    <div class="icon-container">
        <h3>16x16</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>32x32</h3>
        <canvas id="icon32" width="32" height="32"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>48x48</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
    </div>
    
    <div class="icon-container">
        <h3>128x128</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
    </div>

    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制圆形背景
            const margin = Math.max(1, size / 10);
            const radius = (size - margin * 2) / 2;
            const centerX = size / 2;
            const centerY = size / 2;
            
            // 渐变背景
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // 边框
            ctx.strokeStyle = '#4c64d6';
            ctx.lineWidth = Math.max(1, size / 32);
            ctx.stroke();
            
            // 绘制播放按钮三角形
            const triangleSize = size / 3;
            ctx.beginPath();
            ctx.moveTo(centerX - triangleSize/3, centerY - triangleSize/2);
            ctx.lineTo(centerX - triangleSize/3, centerY + triangleSize/2);
            ctx.lineTo(centerX + triangleSize/2, centerY);
            ctx.closePath();
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            
            // 添加B站标识（仅在较大尺寸时）
            if (size >= 32) {
                const bSize = Math.max(4, size / 8);
                ctx.beginPath();
                ctx.arc(size - bSize - margin, margin + bSize, bSize/2, 0, 2 * Math.PI);
                ctx.fillStyle = '#fb7299';
                ctx.fill();
            }
            
            // 自动下载图标
            setTimeout(() => {
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL();
                // link.click(); // 取消自动下载，让用户手动保存
            }, 100);
        }
        
        // 创建所有尺寸的图标
        createIcon('icon16', 16);
        createIcon('icon32', 32);
        createIcon('icon48', 48);
        createIcon('icon128', 128);
        
        console.log('图标已生成，请右键点击图标保存为PNG文件');
    </script>
</body>
</html>
