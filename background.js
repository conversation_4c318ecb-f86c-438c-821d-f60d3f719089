// background.js - 后台服务脚本
class AutoPlayManager {
  constructor() {
    this.isRunning = false;
    this.config = null;
    this.videoList = [];
    this.currentVideoIndex = 0;
    this.currentTabId = null;
    this.controlWindowId = null;
    this.playTimer = null;
    this.status = {
      isRunning: false,
      currentVideo: null,
      videoList: [],
      progress: { current: 0, total: 0 }
    };

    this.bindEvents();
  }

  bindEvents() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听标签页关闭事件
    chrome.tabs.onRemoved.addListener((tabId) => {
      if (tabId === this.currentTabId) {
        this.currentTabId = null;
      }
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'startAutoPlay':
          await this.startAutoPlay(request.config);
          sendResponse({ success: true });
          break;
          
        case 'stopAutoPlay':
          await this.stopAutoPlay();
          sendResponse({ success: true });
          break;
          
        case 'getStatus':
          sendResponse({ status: this.status });
          break;
          
        case 'videoPlayCompleted':
          await this.handleVideoCompleted(request.videoInfo);
          sendResponse({ success: true });
          break;
          
        case 'videosFound':
          this.handleVideosFound(request.videos);
          sendResponse({ success: true });
          break;

        case 'keepAlive':
          // 保持popup连接活跃
          sendResponse({ success: true, timestamp: Date.now() });
          break;

        case 'getDebugInfo':
          // 获取调试信息
          sendResponse({
            success: true,
            debugInfo: {
              isRunning: this.isRunning,
              currentTabId: this.currentTabId,
              videoListLength: this.videoList.length,
              currentVideoIndex: this.currentVideoIndex,
              config: this.config
            }
          });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({ error: error.message });
    }
  }

  async startAutoPlay(config) {
    if (this.isRunning) {
      throw new Error('自动播放已在运行中');
    }

    this.isRunning = true;
    this.config = config;
    this.videoList = [];
    this.currentVideoIndex = 0;
    this.updateStatus();

    console.log('开始自动播放，配置:', config);

    try {
      // 创建控制窗口
      await this.createControlWindow();

      // 等待一下让控制窗口完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 打开B站搜索页面
      const tab = await chrome.tabs.create({
        url: `https://search.bilibili.com/all?keyword=${encodeURIComponent(config.searchKeyword)}`,
        active: true
      });

      this.currentTabId = tab.id;

      // 等待页面加载完成后开始搜索
      setTimeout(async () => {
        try {
          await this.injectSearchScript(tab.id);
        } catch (error) {
          console.error('注入搜索脚本失败:', error);
          await this.stopAutoPlay();
        }
      }, 8000); // 增加等待时间到8秒

    } catch (error) {
      console.error('启动自动播放失败:', error);
      this.isRunning = false;
      this.updateStatus();
      throw error;
    }
  }

  async createControlWindow() {
    try {
      // 创建控制窗口的HTML内容
      const controlWindowUrl = chrome.runtime.getURL('control.html');

      const window = await chrome.windows.create({
        url: controlWindowUrl,
        type: 'popup',
        width: 400,
        height: 600,
        left: 100,
        top: 100,
        focused: true
      });

      this.controlWindowId = window.id;
      console.log('控制窗口已创建:', window.id);

    } catch (error) {
      console.error('创建控制窗口失败:', error);
      // 如果创建窗口失败，继续使用原有逻辑
    }
  }

  async stopAutoPlay() {
    this.isRunning = false;

    if (this.playTimer) {
      clearTimeout(this.playTimer);
      this.playTimer = null;
    }

    // 关闭控制窗口
    if (this.controlWindowId) {
      try {
        await chrome.windows.remove(this.controlWindowId);
      } catch (error) {
        console.error('关闭控制窗口失败:', error);
      }
      this.controlWindowId = null;
    }

    // 关闭当前标签页
    if (this.currentTabId) {
      try {
        await chrome.tabs.remove(this.currentTabId);
      } catch (error) {
        console.error('关闭标签页失败:', error);
      }
      this.currentTabId = null;
    }

    this.updateStatus();
    console.log('自动播放已停止');
  }

  async injectSearchScript(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: this.searchAndCollectVideos,
        args: [this.config.maxVideos]
      });
    } catch (error) {
      console.error('注入脚本失败:', error);
      throw error;
    }
  }

  // 在页面中执行的搜索和收集视频函数
  searchAndCollectVideos(maxVideos) {
    console.log('开始搜索和收集视频链接...');

    let retryCount = 0;
    const maxRetries = 5;

    const collectVideos = () => {
      retryCount++;
      console.log('第 ' + retryCount + ' 次尝试收集视频');
      console.log('页面URL:', window.location.href);
      console.log('页面标题:', document.title);
      console.log('页面加载状态:', document.readyState);

      // 检查页面是否真的是搜索结果页面
      if (!window.location.href.includes('search.bilibili.com')) {
        console.error('当前页面不是B站搜索页面');
        return;
      }

      // 等待搜索结果加载的指示器
      const loadingIndicators = [
        '.search-loading',
        '.loading',
        '.skeleton'
      ];

      let isLoading = false;
      for (const indicator of loadingIndicators) {
        if (document.querySelector(indicator)) {
          isLoading = true;
          console.log('检测到加载指示器:', indicator);
          break;
        }
      }

      if (isLoading && retryCount < maxRetries) {
        console.log('页面仍在加载，等待3秒后重试...');
        setTimeout(collectVideos, 3000);
        return;
      }

      // 更全面的视频选择器，适配B站不同版本的页面结构
      const videoSelectors = [
        // 新版B站搜索页面
        '.video-item .bili-video-card__info--tit a',
        '.bili-video-card .bili-video-card__info--tit a',
        '.video-item .title a',
        '.bili-video-card__info--title a',

        // 旧版B站搜索页面
        '.video-item-title a',
        '.search-item .title a',
        '.search-video-item .title a',

        // 通用视频链接选择器
        'a[href*="/video/BV"]',
        'a[href*="/video/av"]',

        // 其他可能的选择器
        '.video-item .video-item-title a',
        '.video-item__info .video-item__info--title a',
        '.search-result .title a',
        '.result-item .title a'
      ];

      let videoElements = [];
      let usedSelector = '';

      // 先尝试通用的视频链接选择器
      console.log('=== 开始尝试视频选择器 ===');
      for (const selector of videoSelectors) {
        const elements = document.querySelectorAll(selector);
        console.log('选择器:', selector, '→ 找到', elements.length, '个元素');

        if (elements.length > 0) {
          // 验证这些元素是否真的是视频链接
          let validElements = [];
          for (let i = 0; i < elements.length; i++) {
            const element = elements[i];
            const href = element.href;
            if (href && (href.includes('/video/BV') || href.includes('/video/av'))) {
              validElements.push(element);
            }
          }

          if (validElements.length > 0) {
            videoElements = validElements;
            usedSelector = selector;
            console.log('✓ 使用选择器:', selector, '找到', validElements.length, '个有效视频');
            break;
          }
        }
      }

      if (videoElements.length === 0) {
        console.error('未找到任何视频元素');

        // 输出页面调试信息
        console.log('=== 页面调试信息 ===');
        console.log('页面HTML长度:', document.documentElement.innerHTML.length);
        console.log('是否包含video关键词:', document.documentElement.innerHTML.includes('video'));
        console.log('是否包含BV关键词:', document.documentElement.innerHTML.includes('BV'));

        // 尝试查找任何包含video的链接
        const allVideoLinks = document.querySelectorAll('a[href*="video"]');
        console.log('所有包含video的链接数量:', allVideoLinks.length);

        if (allVideoLinks.length > 0) {
          console.log('前5个video链接:');
          for (let i = 0; i < Math.min(5, allVideoLinks.length); i++) {
            console.log(i + 1, allVideoLinks[i].href, allVideoLinks[i].textContent.trim().substring(0, 50));
          }
        }

        if (retryCount < maxRetries) {
          console.log('等待5秒后重试...');
          setTimeout(collectVideos, 5000);
          return;
        } else {
          console.error('达到最大重试次数，停止尝试');
          // 发送空列表
          chrome.runtime.sendMessage({
            action: 'videosFound',
            videos: []
          });
          return;
        }
      }

      const videos = [];

      console.log('=== 开始处理视频元素 ===');
      for (let i = 0; i < Math.min(videoElements.length, maxVideos); i++) {
        const element = videoElements[i];
        const title = element.textContent?.trim() || element.title?.trim() || ('视频 ' + (i + 1));
        let url = element.href;

        console.log('处理视频 ' + (i + 1) + ':');
        console.log('  标题:', title);
        console.log('  URL:', url);

        // 确保URL是完整的
        if (url && !url.startsWith('http')) {
          url = 'https:' + url;
        }

        if (title && url && (url.includes('bilibili.com/video/') || url.includes('/video/BV') || url.includes('/video/av'))) {
          videos.push({
            title: title,
            url: url,
            status: 'pending',
            playedDuration: 0
          });
          console.log('  ✓ 已添加到列表');
        } else {
          console.log('  ✗ 跳过（无效链接）');
        }
      }

      console.log('=== 收集完成 ===');
      console.log('总共收集到', videos.length, '个有效视频');

      // 发送视频列表给background script
      chrome.runtime.sendMessage({
        action: 'videosFound',
        videos: videos
      });
    };

    // 等待页面加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(collectVideos, 3000);
      });
    } else {
      setTimeout(collectVideos, 3000);
    }
  }

  handleVideosFound(videos) {
    this.videoList = videos;
    this.status.videoList = videos;
    this.status.progress.total = videos.length;
    this.updateStatus();
    
    console.log('收到视频列表:', videos);
    
    if (videos.length > 0) {
      // 开始播放第一个视频
      this.playNextVideo();
    } else {
      console.log('未找到视频');
      this.stopAutoPlay();
    }
  }

  async playNextVideo() {
    if (!this.isRunning || this.currentVideoIndex >= this.videoList.length) {
      console.log('播放完成或已停止');
      await this.stopAutoPlay();
      return;
    }

    const video = this.videoList[this.currentVideoIndex];
    video.status = 'playing';
    this.status.currentVideo = video;
    this.status.progress.current = this.currentVideoIndex + 1;
    this.updateStatus();

    console.log(`开始播放第 ${this.currentVideoIndex + 1} 个视频:`, video.title);

    try {
      // 打开视频页面
      const tab = await chrome.tabs.create({
        url: video.url,
        active: true
      });
      
      // 关闭之前的标签页
      if (this.currentTabId && this.currentTabId !== tab.id) {
        try {
          await chrome.tabs.remove(this.currentTabId);
        } catch (error) {
          console.error('关闭旧标签页失败:', error);
        }
      }
      
      this.currentTabId = tab.id;
      
      // 等待页面加载后开始播放
      setTimeout(async () => {
        try {
          await this.injectPlayScript(tab.id, this.config.playDuration);
        } catch (error) {
          console.error('注入播放脚本失败:', error);
          this.handleVideoError(video);
        }
      }, 3000);
      
    } catch (error) {
      console.error('打开视频页面失败:', error);
      this.handleVideoError(video);
    }
  }

  async injectPlayScript(tabId, duration) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: this.playVideoScript,
        args: [duration]
      });
    } catch (error) {
      console.error('注入播放脚本失败:', error);
      throw error;
    }
  }

  // 在视频页面中执行的播放脚本
  playVideoScript(duration) {
    console.log('开始播放视频，时长: ' + duration + '秒');

    // 等待视频元素加载的函数
    function waitForVideo(timeout = 10000) {
      return new Promise((resolve, reject) => {
        const startTime = Date.now();

        function checkVideo() {
          const video = document.querySelector('video');
          if (video) {
            resolve(video);
            return;
          }

          if (Date.now() - startTime > timeout) {
            reject(new Error('等待视频元素超时'));
            return;
          }

          setTimeout(checkVideo, 500);
        }

        checkVideo();
      });
    }

    // 尝试多种播放方法
    async function attemptPlay(video) {
      const playMethods = [
        // 方法1: 直接调用play()
        async () => {
          console.log('尝试方法1: 直接播放');
          await video.play();
          return true;
        },

        // 方法2: 点击播放按钮
        async () => {
          console.log('尝试方法2: 点击播放按钮');
          const playButtons = [
            '.bpx-player-ctrl-play',
            '.bilibili-player-video-btn-start',
            '.bpx-player-ctrl-btn',
            '[aria-label="播放"]',
            '.video-state-pause',
            '.bpx-player-ctrl-play-icon',
            '.bilibili-player-video-control-play',
            '.player-ctrl-play',
            '.video-control-play-icon',
            '[title="播放"]',
            '.bpx-player-ctrl-play-icon',
            '.bilibili-player-video-control .bilibili-player-video-btn-start'
          ];

          for (const selector of playButtons) {
            const button = document.querySelector(selector);
            if (button && !button.disabled && button.offsetParent !== null) {
              console.log('点击播放按钮:', selector);
              button.click();
              await new Promise(resolve => setTimeout(resolve, 1000));
              if (!video.paused) {
                return true;
              }
            }
          }
          return false;
        },

        // 方法3: 点击视频区域
        async () => {
          console.log('尝试方法3: 点击视频区域');
          const videoContainers = [
            '.bpx-player-video-area',
            '.bilibili-player-video',
            '.player-video',
            '.bpx-player-video-wrap',
            'video'
          ];

          for (const selector of videoContainers) {
            const container = document.querySelector(selector);
            if (container) {
              console.log('点击视频容器:', selector);
              // 模拟用户点击
              const rect = container.getBoundingClientRect();
              const clickEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true,
                clientX: rect.left + rect.width / 2,
                clientY: rect.top + rect.height / 2
              });
              container.dispatchEvent(clickEvent);

              await new Promise(resolve => setTimeout(resolve, 1000));
              if (!video.paused) {
                return true;
              }
            }
          }
          return false;
        },

        // 方法4: 键盘空格键
        async () => {
          console.log('尝试方法4: 模拟空格键');
          const spaceEvent = new KeyboardEvent('keydown', {
            key: ' ',
            code: 'Space',
            keyCode: 32,
            bubbles: true
          });
          document.dispatchEvent(spaceEvent);

          await new Promise(resolve => setTimeout(resolve, 1000));
          return !video.paused;
        }
      ];

      for (const method of playMethods) {
        try {
          const success = await method();
          if (success) {
            console.log('播放成功');
            return true;
          }
        } catch (error) {
          console.log('播放方法失败:', error.message);
        }
      }

      return false;
    }

    const playVideo = async () => {
      try {
        // 等待视频元素加载
        const video = await waitForVideo();
        console.log('找到视频元素');

        // 尝试播放
        const playSuccess = await attemptPlay(video);

        if (playSuccess) {
          console.log('视频开始播放');
        } else {
          console.log('所有播放方法都失败，但继续计时');
        }

        // 设置定时器，播放指定时长后停止
        setTimeout(() => {
          if (!video.paused) {
            video.pause();
            console.log('视频播放完成，已暂停');
          }

          // 通知background script播放完成
          chrome.runtime.sendMessage({
            action: 'videoPlayCompleted',
            videoInfo: {
              title: document.title,
              duration: duration,
              url: window.location.href,
              playSuccess: playSuccess
            }
          });
        }, duration * 1000);

      } catch (error) {
        console.error('播放视频时出错:', error);
        // 即使出错也要通知完成，继续下一个视频
        setTimeout(() => {
          chrome.runtime.sendMessage({
            action: 'videoPlayCompleted',
            videoInfo: {
              title: document.title,
              duration: duration,
              url: window.location.href,
              playSuccess: false,
              error: error.message
            }
          });
        }, duration * 1000);
      }
    };

    // 等待页面完全加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(playVideo, 3000);
      });
    } else {
      setTimeout(playVideo, 3000);
    }
  }

  async handleVideoCompleted(videoInfo) {
    const video = this.videoList[this.currentVideoIndex];
    if (video) {
      video.status = 'completed';
      video.playedDuration = this.config.playDuration;
    }
    
    console.log('视频播放完成:', videoInfo);
    
    // 移动到下一个视频
    this.currentVideoIndex++;
    this.updateStatus();
    
    // 播放下一个视频
    setTimeout(() => {
      this.playNextVideo();
    }, 1000);
  }

  handleVideoError(video) {
    video.status = 'error';
    this.currentVideoIndex++;
    this.updateStatus();
    
    // 继续播放下一个视频
    setTimeout(() => {
      this.playNextVideo();
    }, 1000);
  }

  updateStatus() {
    this.status.isRunning = this.isRunning;
    this.status.videoList = this.videoList;
    this.status.progress.current = this.currentVideoIndex;
    this.status.progress.total = this.videoList.length;
    
    if (this.currentVideoIndex < this.videoList.length) {
      this.status.currentVideo = this.videoList[this.currentVideoIndex];
    } else {
      this.status.currentVideo = null;
    }
  }
}

// 创建自动播放管理器实例
const autoPlayManager = new AutoPlayManager();
