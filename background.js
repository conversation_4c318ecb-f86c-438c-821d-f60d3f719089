// background.js - 后台服务脚本
class AutoPlayManager {
  constructor() {
    this.isRunning = false;
    this.config = null;
    this.videoList = [];
    this.currentVideoIndex = 0;
    this.currentTabId = null;
    this.playTimer = null;
    this.status = {
      isRunning: false,
      currentVideo: null,
      videoList: [],
      progress: { current: 0, total: 0 }
    };
    
    this.bindEvents();
  }

  bindEvents() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听标签页关闭事件
    chrome.tabs.onRemoved.addListener((tabId) => {
      if (tabId === this.currentTabId) {
        this.currentTabId = null;
      }
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'startAutoPlay':
          await this.startAutoPlay(request.config);
          sendResponse({ success: true });
          break;
          
        case 'stopAutoPlay':
          await this.stopAutoPlay();
          sendResponse({ success: true });
          break;
          
        case 'getStatus':
          sendResponse({ status: this.status });
          break;
          
        case 'videoPlayCompleted':
          await this.handleVideoCompleted(request.videoInfo);
          sendResponse({ success: true });
          break;
          
        case 'videosFound':
          this.handleVideosFound(request.videos);
          sendResponse({ success: true });
          break;
          
        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({ error: error.message });
    }
  }

  async startAutoPlay(config) {
    if (this.isRunning) {
      throw new Error('自动播放已在运行中');
    }

    this.isRunning = true;
    this.config = config;
    this.videoList = [];
    this.currentVideoIndex = 0;
    this.updateStatus();

    console.log('开始自动播放，配置:', config);

    try {
      // 打开B站搜索页面
      const tab = await chrome.tabs.create({
        url: `https://search.bilibili.com/all?keyword=${encodeURIComponent(config.searchKeyword)}`,
        active: true
      });
      
      this.currentTabId = tab.id;
      
      // 等待页面加载完成后开始搜索
      setTimeout(async () => {
        try {
          await this.injectSearchScript(tab.id);
        } catch (error) {
          console.error('注入搜索脚本失败:', error);
          await this.stopAutoPlay();
        }
      }, 3000);
      
    } catch (error) {
      console.error('创建标签页失败:', error);
      this.isRunning = false;
      this.updateStatus();
      throw error;
    }
  }

  async stopAutoPlay() {
    this.isRunning = false;
    
    if (this.playTimer) {
      clearTimeout(this.playTimer);
      this.playTimer = null;
    }
    
    // 关闭当前标签页
    if (this.currentTabId) {
      try {
        await chrome.tabs.remove(this.currentTabId);
      } catch (error) {
        console.error('关闭标签页失败:', error);
      }
      this.currentTabId = null;
    }
    
    this.updateStatus();
    console.log('自动播放已停止');
  }

  async injectSearchScript(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: this.searchAndCollectVideos,
        args: [this.config.maxVideos]
      });
    } catch (error) {
      console.error('注入脚本失败:', error);
      throw error;
    }
  }

  // 在页面中执行的搜索和收集视频函数
  searchAndCollectVideos(maxVideos) {
    console.log('开始搜索和收集视频链接...');
    
    const collectVideos = () => {
      // 更全面的视频选择器，适配B站不同版本的页面结构
      const videoSelectors = [
        '.video-item .title a',
        '.bili-video-card .bili-video-card__info--tit a',
        '.video-item-title a',
        '.bili-video-card__info--tit a',
        '.video-item .video-item-title a',
        'a[href*="/video/BV"]',
        'a[href*="/video/av"]',
        '.search-item .title a',
        '.search-video-item .title a'
      ];

      let videoElements = [];

      // 尝试不同的选择器
      for (const selector of videoSelectors) {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          videoElements = Array.from(elements);
          console.log('使用选择器 ' + selector + ' 找到 ' + elements.length + ' 个视频');
          break;
        }
      }

      const videos = [];

      for (let i = 0; i < Math.min(videoElements.length, maxVideos); i++) {
        const element = videoElements[i];
        const title = element.textContent?.trim() || element.title?.trim() || ('视频 ' + (i + 1));
        let url = element.href;

        // 确保URL是完整的
        if (url && !url.startsWith('http')) {
          url = 'https:' + url;
        }

        if (title && url && (url.includes('bilibili.com/video/') || url.includes('/video/BV') || url.includes('/video/av'))) {
          videos.push({
            title: title,
            url: url,
            status: 'pending',
            playedDuration: 0
          });
        }
      }
      
      console.log('收集到视频:', videos);
      
      // 发送视频列表给background script
      chrome.runtime.sendMessage({
        action: 'videosFound',
        videos: videos
      });
    };
    
    // 等待页面加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', collectVideos);
    } else {
      setTimeout(collectVideos, 2000);
    }
  }

  handleVideosFound(videos) {
    this.videoList = videos;
    this.status.videoList = videos;
    this.status.progress.total = videos.length;
    this.updateStatus();
    
    console.log('收到视频列表:', videos);
    
    if (videos.length > 0) {
      // 开始播放第一个视频
      this.playNextVideo();
    } else {
      console.log('未找到视频');
      this.stopAutoPlay();
    }
  }

  async playNextVideo() {
    if (!this.isRunning || this.currentVideoIndex >= this.videoList.length) {
      console.log('播放完成或已停止');
      await this.stopAutoPlay();
      return;
    }

    const video = this.videoList[this.currentVideoIndex];
    video.status = 'playing';
    this.status.currentVideo = video;
    this.status.progress.current = this.currentVideoIndex + 1;
    this.updateStatus();

    console.log(`开始播放第 ${this.currentVideoIndex + 1} 个视频:`, video.title);

    try {
      // 打开视频页面
      const tab = await chrome.tabs.create({
        url: video.url,
        active: true
      });
      
      // 关闭之前的标签页
      if (this.currentTabId && this.currentTabId !== tab.id) {
        try {
          await chrome.tabs.remove(this.currentTabId);
        } catch (error) {
          console.error('关闭旧标签页失败:', error);
        }
      }
      
      this.currentTabId = tab.id;
      
      // 等待页面加载后开始播放
      setTimeout(async () => {
        try {
          await this.injectPlayScript(tab.id, this.config.playDuration);
        } catch (error) {
          console.error('注入播放脚本失败:', error);
          this.handleVideoError(video);
        }
      }, 3000);
      
    } catch (error) {
      console.error('打开视频页面失败:', error);
      this.handleVideoError(video);
    }
  }

  async injectPlayScript(tabId, duration) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        func: this.playVideoScript,
        args: [duration]
      });
    } catch (error) {
      console.error('注入播放脚本失败:', error);
      throw error;
    }
  }

  // 在视频页面中执行的播放脚本
  playVideoScript(duration) {
    console.log(`开始播放视频，时长: ${duration}秒`);
    
    const playVideo = () => {
      // 查找视频元素
      const video = document.querySelector('video');
      if (!video) {
        console.error('未找到视频元素');
        return;
      }
      
      // 尝试播放视频
      const playPromise = video.play();
      if (playPromise) {
        playPromise.then(() => {
          console.log('视频开始播放');
          
          // 设置定时器，播放指定时长后停止
          setTimeout(() => {
            video.pause();
            console.log('视频播放完成');
            
            // 通知background script播放完成
            chrome.runtime.sendMessage({
              action: 'videoPlayCompleted',
              videoInfo: {
                title: document.title,
                duration: duration,
                url: window.location.href
              }
            });
          }, duration * 1000);
          
        }).catch(error => {
          console.error('视频播放失败:', error);
          // 尝试点击播放按钮
          const playButtons = [
            '.bpx-player-ctrl-play',
            '.bilibili-player-video-btn-start',
            '.bpx-player-ctrl-btn',
            '[aria-label="播放"]',
            '.video-state-pause',
            '.bpx-player-ctrl-play-icon',
            '.bilibili-player-video-control-play',
            '.player-ctrl-play',
            '.video-control-play-icon',
            '[title="播放"]'
          ];

          let playSuccess = false;
          for (const selector of playButtons) {
            const playBtn = document.querySelector(selector);
            if (playBtn && !playBtn.disabled) {
              console.log('尝试点击播放按钮:', selector);
              playBtn.click();
              playSuccess = true;
              break;
            }
          }

          // 如果还是无法播放，尝试点击视频区域
          if (!playSuccess) {
            const videoContainer = document.querySelector('.bpx-player-video-area, .bilibili-player-video, .player-video');
            if (videoContainer) {
              console.log('尝试点击视频区域');
              videoContainer.click();
            }
          }

          setTimeout(() => {
            chrome.runtime.sendMessage({
              action: 'videoPlayCompleted',
              videoInfo: {
                title: document.title,
                duration: duration,
                url: window.location.href
              }
            });
          }, duration * 1000);
        });
      }
    };
    
    // 等待页面完全加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(playVideo, 2000);
      });
    } else {
      setTimeout(playVideo, 2000);
    }
  }

  async handleVideoCompleted(videoInfo) {
    const video = this.videoList[this.currentVideoIndex];
    if (video) {
      video.status = 'completed';
      video.playedDuration = this.config.playDuration;
    }
    
    console.log('视频播放完成:', videoInfo);
    
    // 移动到下一个视频
    this.currentVideoIndex++;
    this.updateStatus();
    
    // 播放下一个视频
    setTimeout(() => {
      this.playNextVideo();
    }, 1000);
  }

  handleVideoError(video) {
    video.status = 'error';
    this.currentVideoIndex++;
    this.updateStatus();
    
    // 继续播放下一个视频
    setTimeout(() => {
      this.playNextVideo();
    }, 1000);
  }

  updateStatus() {
    this.status.isRunning = this.isRunning;
    this.status.videoList = this.videoList;
    this.status.progress.current = this.currentVideoIndex;
    this.status.progress.total = this.videoList.length;
    
    if (this.currentVideoIndex < this.videoList.length) {
      this.status.currentVideo = this.videoList[this.currentVideoIndex];
    } else {
      this.status.currentVideo = null;
    }
  }
}

// 创建自动播放管理器实例
const autoPlayManager = new AutoPlayManager();
