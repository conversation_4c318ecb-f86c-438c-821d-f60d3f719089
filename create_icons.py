#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成Chrome插件所需的图标文件
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    margin = size // 10
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(102, 126, 234, 255), outline=(76, 100, 200, 255), width=2)
    
    # 绘制播放按钮三角形
    center_x, center_y = size // 2, size // 2
    triangle_size = size // 3
    
    # 三角形顶点
    points = [
        (center_x - triangle_size//3, center_y - triangle_size//2),
        (center_x - triangle_size//3, center_y + triangle_size//2),
        (center_x + triangle_size//2, center_y)
    ]
    
    draw.polygon(points, fill=(255, 255, 255, 255))
    
    # 添加小的B站标识
    if size >= 32:
        # 绘制小圆圈表示B站
        circle_size = size // 8
        circle_x = size - circle_size - margin
        circle_y = margin
        draw.ellipse([circle_x, circle_y, circle_x + circle_size, circle_y + circle_size],
                    fill=(251, 114, 153, 255))
    
    # 保存图标
    img.save(filename, 'PNG')
    print(f"已创建图标: {filename} ({size}x{size})")

def main():
    """主函数"""
    # 创建icons目录
    if not os.path.exists('icons'):
        os.makedirs('icons')
    
    # 创建不同尺寸的图标
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = f'icons/icon{size}.png'
        create_icon(size, filename)
    
    print("所有图标创建完成！")

if __name__ == '__main__':
    main()
