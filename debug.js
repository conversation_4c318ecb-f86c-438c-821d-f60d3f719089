// debug.js - 调试页面脚本
class DebugTool {
    constructor() {
        this.logElement = null;
        this.statusElement = null;
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.logElement = document.getElementById('logOutput');
            this.statusElement = document.getElementById('statusInfo');
            
            // 绑定事件
            this.bindEvents();
            
            // 开始定期更新状态
            this.startStatusUpdates();
            
            this.log('调试工具已加载');
        });
    }

    bindEvents() {
        // 绑定按钮事件
        document.getElementById('startTestBtn').addEventListener('click', () => this.startTest());
        document.getElementById('stopTestBtn').addEventListener('click', () => this.stopTest());
        document.getElementById('getStatusBtn').addEventListener('click', () => this.getStatus());
        document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());
        document.getElementById('openBilibiliBtn').addEventListener('click', () => this.openBilibiliSearch());
        document.getElementById('testSelectorsBtn').addEventListener('click', () => this.testVideoSelectors());
        document.getElementById('injectDebugBtn').addEventListener('click', () => this.injectDebugScript());
        document.getElementById('quickTestBtn').addEventListener('click', () => this.quickTest());
    }

    log(message) {
        if (!this.logElement) return;
        const timestamp = new Date().toLocaleTimeString();
        this.logElement.textContent += `[${timestamp}] ${message}\n`;
        this.logElement.scrollTop = this.logElement.scrollHeight;
    }

    clearLog() {
        if (this.logElement) {
            this.logElement.textContent = '';
        }
    }

    updateStatus(message, type = 'info') {
        if (!this.statusElement) return;
        this.statusElement.textContent = message;
        this.statusElement.className = `status ${type}`;
    }

    async startTest() {
        const config = {
            searchKeyword: document.getElementById('searchKeyword').value,
            playDuration: parseInt(document.getElementById('playDuration').value),
            maxVideos: parseInt(document.getElementById('maxVideos').value)
        };

        this.log('开始测试，配置: ' + JSON.stringify(config));
        this.updateStatus('正在启动测试...', 'info');

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'startAutoPlay',
                config: config
            });

            if (response && response.success) {
                this.log('✓ 测试启动成功');
                this.updateStatus('测试已启动', 'success');
            } else {
                this.log('✗ 测试启动失败: ' + (response ? response.error : '未知错误'));
                this.updateStatus('测试启动失败', 'error');
            }
        } catch (error) {
            this.log('✗ 发送消息失败: ' + error.message);
            this.updateStatus('通信错误', 'error');
        }
    }

    async stopTest() {
        this.log('停止测试');
        this.updateStatus('正在停止测试...', 'info');

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'stopAutoPlay'
            });

            if (response && response.success) {
                this.log('✓ 测试已停止');
                this.updateStatus('测试已停止', 'success');
            } else {
                this.log('✗ 停止失败: ' + (response ? response.error : '未知错误'));
                this.updateStatus('停止失败', 'error');
            }
        } catch (error) {
            this.log('✗ 发送消息失败: ' + error.message);
            this.updateStatus('通信错误', 'error');
        }
    }

    async getStatus() {
        this.log('获取状态信息');

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getStatus'
            });

            if (response && response.status) {
                const status = response.status;
                this.log('状态信息: ' + JSON.stringify(status, null, 2));
                this.updateStatus(`运行中: ${status.isRunning}, 视频: ${status.progress.current}/${status.progress.total}`, 'info');
            } else {
                this.log('✗ 获取状态失败');
                this.updateStatus('获取状态失败', 'error');
            }
        } catch (error) {
            this.log('✗ 发送消息失败: ' + error.message);
            this.updateStatus('通信错误', 'error');
        }
    }

    async openBilibiliSearch() {
        const keyword = document.getElementById('searchKeyword').value;
        const url = `https://search.bilibili.com/all?keyword=${encodeURIComponent(keyword)}`;
        
        this.log('打开B站搜索页面: ' + url);
        
        try {
            const tab = await chrome.tabs.create({ url: url, active: true });
            this.log('✓ 页面已打开，标签页ID: ' + tab.id);
            this.updateStatus('B站搜索页面已打开', 'success');
        } catch (error) {
            this.log('✗ 打开页面失败: ' + error.message);
            this.updateStatus('打开页面失败', 'error');
        }
    }

    async testVideoSelectors() {
        this.log('测试视频选择器...');

        try {
            // 查找B站搜索页面
            const tabs = await chrome.tabs.query({});
            const bilibiliTabs = tabs.filter(tab =>
                tab.url && tab.url.includes('search.bilibili.com')
            );

            if (bilibiliTabs.length === 0) {
                this.log('✗ 没有找到B站搜索页面');
                this.log('请先点击"打开B站搜索页面"按钮');
                this.updateStatus('未找到B站页面', 'error');
                return;
            }

            const tabId = bilibiliTabs[0].id;
            this.log('在B站搜索页面 (标签页 ' + tabId + ') 中测试选择器');

            // 切换到B站页面
            await chrome.tabs.update(tabId, { active: true });

            const results = await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: () => {
                    const results = {
                        url: window.location.href,
                        title: document.title,
                        readyState: document.readyState,
                        selectors: []
                    };

                    console.log('=== 视频选择器测试 ===');
                    console.log('页面URL:', results.url);
                    console.log('页面标题:', results.title);
                    console.log('加载状态:', results.readyState);

                    const selectors = [
                        '.video-item .bili-video-card__info--tit a',
                        '.bili-video-card .bili-video-card__info--tit a',
                        '.video-item .title a',
                        '.bili-video-card__info--title a',
                        'a[href*="/video/BV"]',
                        'a[href*="/video/av"]',
                        '.search-item .title a',
                        '.result-item .title a'
                    ];

                    selectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        const count = elements.length;

                        console.log(`选择器 "${selector}": ${count} 个元素`);

                        const selectorResult = {
                            selector: selector,
                            count: count,
                            samples: []
                        };

                        if (count > 0) {
                            console.log('前3个元素:');
                            for (let i = 0; i < Math.min(3, count); i++) {
                                const element = elements[i];
                                const text = element.textContent.trim().substring(0, 50);
                                const href = element.href;
                                console.log(`  ${i+1}. ${text} - ${href}`);
                                selectorResult.samples.push({ text, href });
                            }
                        }

                        results.selectors.push(selectorResult);
                    });

                    // 额外信息
                    const allVideoLinks = document.querySelectorAll('a[href*="/video/"]');
                    const bvLinks = document.querySelectorAll('a[href*="/video/BV"]');

                    results.totalVideoLinks = allVideoLinks.length;
                    results.bvLinks = bvLinks.length;

                    console.log('总视频链接数:', results.totalVideoLinks);
                    console.log('BV链接数:', results.bvLinks);

                    return results;
                }
            });

            const result = results[0].result;

            this.log('=== 测试结果 ===');
            this.log('页面URL: ' + result.url);
            this.log('页面标题: ' + result.title);
            this.log('加载状态: ' + result.readyState);
            this.log('总视频链接: ' + result.totalVideoLinks);
            this.log('BV链接: ' + result.bvLinks);

            this.log('=== 选择器结果 ===');
            let foundVideos = false;
            result.selectors.forEach(sel => {
                this.log(`${sel.selector}: ${sel.count} 个元素`);
                if (sel.count > 0) {
                    foundVideos = true;
                    sel.samples.forEach((sample, i) => {
                        this.log(`  ${i+1}. ${sample.text} - ${sample.href}`);
                    });
                }
            });

            if (foundVideos) {
                this.log('✓ 找到视频元素！');
                this.updateStatus('找到视频元素', 'success');
            } else {
                this.log('✗ 未找到任何视频元素');
                this.updateStatus('未找到视频元素', 'error');
            }

        } catch (error) {
            this.log('✗ 测试失败: ' + error.message);
            this.updateStatus('测试失败', 'error');
        }
    }

    async injectDebugScript() {
        this.log('注入调试脚本...');

        try {
            // 查找B站搜索页面
            const tabs = await chrome.tabs.query({});
            const bilibiliTabs = tabs.filter(tab =>
                tab.url && tab.url.includes('search.bilibili.com')
            );

            if (bilibiliTabs.length === 0) {
                this.log('✗ 没有找到B站搜索页面');
                this.log('请先点击"打开B站搜索页面"按钮');
                this.updateStatus('未找到B站页面', 'error');
                return;
            }

            const tabId = bilibiliTabs[0].id;
            this.log('在B站搜索页面 (标签页 ' + tabId + ') 中注入调试脚本');

            // 切换到B站页面
            await chrome.tabs.update(tabId, { active: true });
            
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: () => {
                    // 移除已存在的调试面板
                    const existingPanel = document.getElementById('bilibili-debug-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                    }
                    
                    // 创建调试面板
                    const debugPanel = document.createElement('div');
                    debugPanel.id = 'bilibili-debug-panel';
                    debugPanel.style.cssText = `
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        width: 300px;
                        background: white;
                        border: 2px solid #4CAF50;
                        border-radius: 8px;
                        padding: 15px;
                        z-index: 10000;
                        font-family: Arial, sans-serif;
                        font-size: 12px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                    `;
                    
                    const closeButton = document.createElement('button');
                    closeButton.textContent = '关闭';
                    closeButton.style.cssText = `
                        margin-top: 10px;
                        padding: 5px 10px;
                        background: #f44336;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    `;
                    closeButton.addEventListener('click', () => {
                        debugPanel.remove();
                    });
                    
                    const infoDiv = document.createElement('div');
                    infoDiv.id = 'debug-info';
                    
                    debugPanel.innerHTML = '<h3 style="margin: 0 0 10px 0; color: #4CAF50;">🔧 调试面板</h3>';
                    debugPanel.appendChild(infoDiv);
                    debugPanel.appendChild(closeButton);
                    
                    document.body.appendChild(debugPanel);
                    
                    // 更新调试信息
                    function updateDebugInfo() {
                        const info = document.getElementById('debug-info');
                        if (!info) return;
                        
                        const videoLinks = document.querySelectorAll('a[href*="/video/"]');
                        const bvLinks = document.querySelectorAll('a[href*="/video/BV"]');
                        
                        info.innerHTML = `
                            <div><strong>页面信息:</strong></div>
                            <div>URL: ${window.location.href}</div>
                            <div>标题: ${document.title}</div>
                            <div>加载状态: ${document.readyState}</div>
                            <div><strong>视频链接:</strong></div>
                            <div>总video链接: ${videoLinks.length}</div>
                            <div>BV链接: ${bvLinks.length}</div>
                            <div><strong>页面元素:</strong></div>
                            <div>总链接数: ${document.querySelectorAll('a').length}</div>
                            <div>总div数: ${document.querySelectorAll('div').length}</div>
                        `;
                    }
                    
                    updateDebugInfo();
                    const updateInterval = setInterval(() => {
                        if (document.getElementById('debug-info')) {
                            updateDebugInfo();
                        } else {
                            clearInterval(updateInterval);
                        }
                    }, 2000);
                    
                    console.log('调试面板已注入');
                }
            });

            this.log('✓ 调试脚本注入成功');
            this.updateStatus('调试脚本已注入', 'success');
        } catch (error) {
            this.log('✗ 注入失败: ' + error.message);
            this.updateStatus('注入失败', 'error');
        }
    }

    async quickTest() {
        this.log('=== 开始快速测试 ===');

        try {
            // 1. 打开B站搜索页面
            this.log('步骤1: 打开B站搜索页面');
            await this.openBilibiliSearch();

            // 等待页面加载
            this.log('等待页面加载...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // 2. 测试视频选择器
            this.log('步骤2: 测试视频选择器');
            await this.testVideoSelectors();

            // 3. 尝试启动自动播放
            this.log('步骤3: 测试自动播放功能');
            const config = {
                searchKeyword: document.getElementById('searchKeyword').value,
                playDuration: 10, // 短时间测试
                maxVideos: 2 // 少量视频测试
            };

            const response = await chrome.runtime.sendMessage({
                action: 'startAutoPlay',
                config: config
            });

            if (response && response.success) {
                this.log('✓ 自动播放启动成功');
                this.updateStatus('快速测试完成', 'success');
            } else {
                this.log('✗ 自动播放启动失败');
                this.updateStatus('自动播放失败', 'error');
            }

        } catch (error) {
            this.log('✗ 快速测试失败: ' + error.message);
            this.updateStatus('快速测试失败', 'error');
        }
    }

    startStatusUpdates() {
        // 定期更新状态
        setInterval(async () => {
            try {
                const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
                if (response && response.status && response.status.isRunning) {
                    const status = response.status;
                    this.updateStatus(`运行中: ${status.progress.current}/${status.progress.total}`, 'info');
                }
            } catch (error) {
                // 忽略错误
            }
        }, 2000);
    }
}

// 初始化调试工具
new DebugTool();
