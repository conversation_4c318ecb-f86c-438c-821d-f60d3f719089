<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>B站自动播放器 - 控制面板</title>
  <style>
    body {
      width: 380px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: 0;
      min-height: 500px;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
      padding: 15px;
      background: rgba(255,255,255,0.1);
      border-radius: 10px;
      backdrop-filter: blur(10px);
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .status-section {
      background: rgba(255,255,255,0.1);
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 15px;
      backdrop-filter: blur(10px);
    }
    
    .status {
      text-align: center;
      margin-bottom: 15px;
      padding: 10px;
      background: rgba(255,255,255,0.1);
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
    }
    
    .progress {
      margin: 15px 0;
    }
    
    .progress-bar {
      width: 100%;
      height: 8px;
      background: rgba(255,255,255,0.3);
      border-radius: 4px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background: #4CAF50;
      width: 0%;
      transition: width 0.3s ease;
    }
    
    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 12px;
      opacity: 0.9;
    }
    
    .video-list {
      max-height: 300px;
      overflow-y: auto;
      margin-top: 15px;
    }
    
    .video-item {
      padding: 10px;
      margin: 8px 0;
      background: rgba(255,255,255,0.1);
      border-radius: 6px;
      font-size: 12px;
      border-left: 4px solid transparent;
    }
    
    .video-item.pending {
      border-left-color: #ffc107;
    }
    
    .video-item.playing {
      border-left-color: #28a745;
      background: rgba(40, 167, 69, 0.2);
      animation: pulse 2s infinite;
    }
    
    .video-item.completed {
      border-left-color: #17a2b8;
      background: rgba(23, 162, 184, 0.2);
    }
    
    .video-item.error {
      border-left-color: #dc3545;
      background: rgba(220, 53, 69, 0.2);
    }
    
    .video-title {
      font-weight: bold;
      margin-bottom: 5px;
      line-height: 1.3;
    }
    
    .video-info {
      font-size: 11px;
      opacity: 0.8;
    }
    
    .controls {
      position: fixed;
      bottom: 20px;
      left: 20px;
      right: 20px;
      background: rgba(255,255,255,0.1);
      padding: 15px;
      border-radius: 10px;
      backdrop-filter: blur(10px);
    }
    
    .stop-btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      background: #dc3545;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
    }
    
    .stop-btn:hover {
      background: #c82333;
      transform: translateY(-2px);
    }
    
    .running-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      background: #4CAF50;
      border-radius: 50%;
      margin-right: 8px;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.1); }
      100% { opacity: 1; transform: scale(1); }
    }
    
    .config-info {
      background: rgba(255,255,255,0.05);
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 15px;
      font-size: 12px;
    }
    
    .config-item {
      margin: 3px 0;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎬 B站自动播放器</h1>
    <div class="running-indicator"></div>
    <span>正在运行中...</span>
  </div>
  
  <div class="config-info" id="configInfo">
    <div class="config-item">搜索关键词: <span id="searchKeyword">-</span></div>
    <div class="config-item">播放时长: <span id="playDuration">-</span>秒</div>
    <div class="config-item">最大数量: <span id="maxVideos">-</span>个</div>
  </div>
  
  <div class="status-section">
    <div class="status" id="status">正在初始化...</div>
    
    <div class="progress">
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
      <div class="progress-text" id="progressText">0 / 0</div>
    </div>
  </div>
  
  <div class="video-list" id="videoList">
    <div style="text-align: center; opacity: 0.7; padding: 20px;">
      等待视频列表加载...
    </div>
  </div>
  
  <div class="controls">
    <button id="stopBtn" class="stop-btn">停止播放</button>
  </div>
  
  <script src="control.js"></script>
</body>
</html>
