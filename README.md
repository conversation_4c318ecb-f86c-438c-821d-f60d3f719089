# B站视频自动播放器 Chrome插件

一个功能强大的Chrome Manifest V3插件，可以自动搜索并播放B站视频，支持自定义播放参数和播放记录。

## 功能特性

- 🔍 **自动搜索**: 自动打开B站并搜索指定关键词
- 🎬 **智能播放**: 依次播放搜索结果中的视频
- ⏱️ **时长控制**: 可设置每个视频的播放时长（10-300秒）
- 📊 **播放记录**: 记录视频名称、播放时间、完成状态等信息
- 🎛️ **参数配置**: 支持自定义搜索关键词、播放时长、播放数量
- 🖥️ **实时显示**: Popup页面实时显示播放进度和状态
- 🚫 **防干扰**: 模拟用户行为，避免页面防爬技术影响

## 安装方法

### 1. 生成图标文件

首先需要生成插件所需的图标文件：

```bash
# 方法1: 使用Node.js生成SVG图标
node generate_icons.js

# 然后打开生成的 svg_to_png.html 文件，点击下载按钮获取PNG图标
```

或者

```bash
# 方法2: 使用Python生成PNG图标（需要安装PIL）
pip install Pillow
python create_icons.py
```

### 2. 加载插件

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹

## 使用方法

### 1. 基本使用

1. 点击浏览器工具栏中的插件图标
2. 在弹出窗口中设置参数：
   - **搜索关键词**: 要搜索的内容（默认：mcp技术）
   - **播放时长**: 每个视频播放秒数（默认：30秒）
   - **最大播放数量**: 最多播放几个视频（默认：5个）
3. 点击"开始播放"按钮
4. 插件会自动：
   - 打开B站搜索页面
   - 搜索指定关键词
   - 依次播放搜索结果中的视频
   - 每个视频播放指定时长后自动切换

### 2. 监控播放状态

- **实时状态**: 弹窗显示当前播放状态
- **进度条**: 显示整体播放进度
- **视频列表**: 显示所有视频的播放状态
  - 🟡 等待中
  - 🟢 播放中  
  - ✅ 已完成
  - ❌ 错误

### 3. 停止播放

点击"停止播放"按钮可随时停止自动播放。

## 技术实现

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Popup UI      │    │  Background     │    │  Content Script │
│   (popup.js)    │◄──►│  (background.js)│◄──►│  (content.js)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
   用户界面交互            服务协调管理              页面内容操作
```

### 核心功能

1. **搜索视频**: 使用DOM操作收集搜索结果中的视频链接
2. **播放控制**: 通过多种方式尝试播放视频：
   - 直接调用 `video.play()`
   - 点击播放按钮
   - 模拟用户点击视频区域
3. **时长控制**: 使用 `setTimeout` 精确控制播放时长
4. **标签页管理**: 自动打开和关闭视频标签页
5. **状态同步**: 实时同步播放状态到弹窗界面

### 防反爬策略

- 模拟真实用户行为
- 随机延迟和等待
- 使用多种播放触发方式
- 避免频繁请求

## 文件结构

```
bilibili-auto-player/
├── manifest.json          # 插件配置文件
├── popup.html            # 弹窗界面
├── popup.js              # 弹窗逻辑
├── background.js         # 后台服务脚本
├── content.js            # 内容脚本
├── generate_icons.js     # 图标生成脚本
├── create_icons.py       # Python图标生成脚本
├── svg_to_png.html       # SVG转PNG工具
├── icons/                # 图标文件夹
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # 说明文档
```

## 配置选项

| 参数 | 默认值 | 范围 | 说明 |
|------|--------|------|------|
| 搜索关键词 | "mcp技术" | 任意文本 | B站搜索的关键词 |
| 播放时长 | 30秒 | 10-300秒 | 每个视频播放的时长 |
| 最大播放数量 | 5个 | 1-20个 | 最多播放的视频数量 |

## 注意事项

1. **网络环境**: 确保网络连接稳定，能正常访问B站
2. **浏览器权限**: 插件需要访问B站页面的权限
3. **页面加载**: 等待页面完全加载后再开始操作
4. **播放限制**: 某些视频可能因版权或其他原因无法播放
5. **资源占用**: 长时间运行可能占用较多系统资源

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查是否开启开发者模式
   - 确认所有文件完整
   - 查看控制台错误信息

2. **搜索无结果**
   - 检查网络连接
   - 尝试更换搜索关键词
   - 确认B站页面正常加载

3. **视频无法播放**
   - 检查视频是否需要登录
   - 尝试手动播放测试
   - 查看浏览器控制台错误

4. **播放中断**
   - 检查网络稳定性
   - 确认没有其他插件冲突
   - 重启插件重试

### 调试方法

1. 打开Chrome开发者工具
2. 查看Console面板的日志信息
3. 检查Network面板的网络请求
4. 使用插件的调试模式

## 更新日志

### v1.0.0 (2024-06-18)
- 初始版本发布
- 支持自动搜索和播放B站视频
- 实现播放时长控制
- 添加播放状态记录
- 提供用户友好的配置界面

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 免责声明

本插件仅供学习和研究使用，请遵守B站的使用条款和相关法律法规。使用本插件产生的任何问题由使用者自行承担。
