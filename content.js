// content.js - 内容脚本，在B站页面中运行
class BilibiliContentScript {
  constructor() {
    this.isAutoPlaying = false;
    this.playTimer = null;
    this.init();
  }

  init() {
    console.log('B站内容脚本已加载');
    
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true;
    });

    // 页面加载完成后的初始化
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.onPageReady();
      });
    } else {
      this.onPageReady();
    }
  }

  onPageReady() {
    console.log('页面准备就绪');
    
    // 如果是搜索页面，自动收集视频链接
    if (window.location.href.includes('search.bilibili.com')) {
      this.handleSearchPage();
    }
    
    // 如果是视频页面，准备播放控制
    if (window.location.href.includes('bilibili.com/video/')) {
      this.handleVideoPage();
    }
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'collectVideos':
        this.collectVideos(request.maxVideos).then(videos => {
          sendResponse({ videos });
        });
        break;
        
      case 'playVideo':
        this.playVideo(request.duration).then(result => {
          sendResponse(result);
        });
        break;
        
      case 'stopVideo':
        this.stopVideo();
        sendResponse({ success: true });
        break;
        
      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  handleSearchPage() {
    console.log('处理搜索页面');
    
    // 等待搜索结果加载
    const waitForResults = () => {
      const videoElements = document.querySelectorAll('.video-item, .bili-video-card');
      if (videoElements.length > 0) {
        console.log(`找到 ${videoElements.length} 个视频元素`);
        return true;
      }
      return false;
    };

    // 轮询等待搜索结果
    const checkResults = () => {
      if (waitForResults()) {
        console.log('搜索结果已加载');
      } else {
        setTimeout(checkResults, 1000);
      }
    };

    setTimeout(checkResults, 2000);
  }

  async collectVideos(maxVideos = 10) {
    console.log('开始收集视频链接...');
    
    const videos = [];
    
    // 等待页面完全加载
    await this.waitForElement('.video-item, .bili-video-card', 5000);
    
    // 收集视频链接 - 使用更全面的选择器
    const videoSelectors = [
      '.video-item .title a',
      '.bili-video-card .bili-video-card__info--tit a',
      '.video-item-title a',
      '.bili-video-card__info--tit a',
      '.video-item .video-item-title a',
      'a[href*="/video/BV"]',
      'a[href*="/video/av"]',
      '.search-item .title a',
      '.search-video-item .title a'
    ];

    let videoElements = [];

    // 尝试不同的选择器
    for (const selector of videoSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        videoElements = Array.from(elements);
        console.log(`使用选择器 ${selector} 找到 ${elements.length} 个视频`);
        break;
      }
    }
    
    console.log(`找到 ${videoElements.length} 个视频链接`);
    
    for (let i = 0; i < Math.min(videoElements.length, maxVideos); i++) {
      const element = videoElements[i];
      const title = element.textContent?.trim() || element.title?.trim() || `视频 ${i + 1}`;
      let url = element.href;
      
      // 确保URL是完整的
      if (url && !url.startsWith('http')) {
        url = 'https:' + url;
      }
      
      if (title && url && url.includes('bilibili.com/video/')) {
        videos.push({
          title: title,
          url: url,
          status: 'pending',
          playedDuration: 0,
          index: i
        });
      }
    }
    
    console.log('收集到的视频:', videos);
    return videos;
  }

  handleVideoPage() {
    console.log('处理视频页面');
    
    // 等待视频元素加载
    this.waitForElement('video', 10000).then(() => {
      console.log('视频元素已加载');
    }).catch(error => {
      console.error('等待视频元素超时:', error);
    });
  }

  async playVideo(duration = 30) {
    console.log(`开始播放视频，时长: ${duration}秒`);
    
    try {
      // 等待视频元素
      const video = await this.waitForElement('video', 10000);
      
      if (!video) {
        throw new Error('未找到视频元素');
      }

      // 尝试播放视频
      const playResult = await this.attemptPlay(video);
      
      if (playResult.success) {
        console.log('视频开始播放');
        
        // 设置播放定时器
        this.playTimer = setTimeout(() => {
          this.stopVideo();
          
          // 通知播放完成
          chrome.runtime.sendMessage({
            action: 'videoPlayCompleted',
            videoInfo: {
              title: document.title,
              duration: duration,
              url: window.location.href,
              actualDuration: duration
            }
          });
          
        }, duration * 1000);
        
        return { success: true, message: '视频播放开始' };
      } else {
        throw new Error(playResult.error);
      }
      
    } catch (error) {
      console.error('播放视频失败:', error);
      return { success: false, error: error.message };
    }
  }

  async attemptPlay(video) {
    try {
      // 方法1: 直接调用play()
      const playPromise = video.play();
      
      if (playPromise) {
        await playPromise;
        return { success: true };
      }
      
    } catch (error) {
      console.log('直接播放失败，尝试点击播放按钮:', error);
      
      // 方法2: 点击播放按钮
      const playButtons = [
        '.bpx-player-ctrl-play',
        '.bilibili-player-video-btn-start',
        '.bpx-player-ctrl-btn',
        '[aria-label="播放"]',
        '.video-state-pause',
        '.bpx-player-ctrl-play-icon',
        '.bilibili-player-video-control-play',
        '.player-ctrl-play',
        '.video-control-play-icon',
        '[title="播放"]'
      ];
      
      for (const selector of playButtons) {
        const button = document.querySelector(selector);
        if (button && !button.disabled) {
          console.log('点击播放按钮:', selector);
          button.click();
          
          // 等待一下看是否开始播放
          await this.sleep(1000);
          
          if (!video.paused) {
            return { success: true };
          }
        }
      }
      
      // 方法3: 模拟用户点击视频区域
      console.log('尝试点击视频区域');
      const videoContainer = document.querySelector('.bpx-player-video-area, .bilibili-player-video');
      if (videoContainer) {
        this.simulateClick(videoContainer);
        await this.sleep(1000);
        
        if (!video.paused) {
          return { success: true };
        }
      }
      
      return { success: false, error: '所有播放方法都失败了' };
    }
  }

  stopVideo() {
    console.log('停止视频播放');
    
    if (this.playTimer) {
      clearTimeout(this.playTimer);
      this.playTimer = null;
    }
    
    const video = document.querySelector('video');
    if (video) {
      video.pause();
    }
  }

  simulateClick(element) {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
      clientX: element.offsetLeft + element.offsetWidth / 2,
      clientY: element.offsetTop + element.offsetHeight / 2
    });
    element.dispatchEvent(event);
  }

  waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`等待元素超时: ${selector}`));
      }, timeout);
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 初始化内容脚本
const bilibiliContentScript = new BilibiliContentScript();
