<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>功能完整性测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .test-item.success {
            background: #d4edda;
            color: #155724;
        }
        .test-item.error {
            background: #f8d7da;
            color: #721c24;
        }
        .test-item.pending {
            background: #fff3cd;
            color: #856404;
        }
        .button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .button:hover {
            background: #45a049;
        }
        .button.test {
            background: #2196F3;
        }
        .button.test:hover {
            background: #1976D2;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 B站自动播放器功能测试</h1>
        <p>验证所有功能是否正常工作</p>
    </div>

    <div class="test-section">
        <h3>📋 基础功能测试</h3>
        
        <div class="test-item pending" id="test-popup">
            <span>Popup界面加载</span>
            <div>
                <span class="status" id="status-popup">待测试</span>
                <button class="button test" onclick="testPopup()">测试</button>
            </div>
        </div>
        
        <div class="test-item pending" id="test-background">
            <span>Background脚本通信</span>
            <div>
                <span class="status" id="status-background">待测试</span>
                <button class="button test" onclick="testBackground()">测试</button>
            </div>
        </div>
        
        <div class="test-item pending" id="test-storage">
            <span>配置存储功能</span>
            <div>
                <span class="status" id="status-storage">待测试</span>
                <button class="button test" onclick="testStorage()">测试</button>
            </div>
        </div>
        
        <div class="test-item pending" id="test-tabs">
            <span>标签页操作权限</span>
            <div>
                <span class="status" id="status-tabs">待测试</span>
                <button class="button test" onclick="testTabs()">测试</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🎬 播放功能测试</h3>
        
        <div class="test-item pending" id="test-bilibili">
            <span>B站页面访问</span>
            <div>
                <span class="status" id="status-bilibili">待测试</span>
                <button class="button test" onclick="testBilibili()">测试</button>
            </div>
        </div>
        
        <div class="test-item pending" id="test-search">
            <span>视频搜索功能</span>
            <div>
                <span class="status" id="status-search">待测试</span>
                <button class="button test" onclick="testSearch()">测试</button>
            </div>
        </div>
        
        <div class="test-item pending" id="test-control">
            <span>控制窗口创建</span>
            <div>
                <span class="status" id="status-control">待测试</span>
                <button class="button test" onclick="testControl()">测试</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🔧 调试功能测试</h3>
        
        <div class="test-item pending" id="test-debug">
            <span>调试页面访问</span>
            <div>
                <span class="status" id="status-debug">待测试</span>
                <button class="button test" onclick="testDebug()">测试</button>
            </div>
        </div>
        
        <div class="test-item pending" id="test-inject">
            <span>脚本注入功能</span>
            <div>
                <span class="status" id="status-inject">待测试</span>
                <button class="button test" onclick="testInject()">测试</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🚀 一键测试</h3>
        <button class="button" onclick="runAllTests()" style="width: 100%; padding: 15px; font-size: 16px;">
            运行所有测试
        </button>
        
        <div class="log" id="testLog">等待测试开始...</div>
    </div>

    <script>
        let logElement = document.getElementById('testLog');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(testId, status, message) {
            const item = document.getElementById(`test-${testId}`);
            const statusSpan = document.getElementById(`status-${testId}`);
            
            item.className = `test-item ${status}`;
            statusSpan.textContent = message;
            statusSpan.className = `status ${status}`;
        }
        
        async function testPopup() {
            log('测试Popup界面...');
            try {
                // 测试popup相关API
                const tabs = await chrome.tabs.query({active: true, currentWindow: true});
                if (tabs.length > 0) {
                    updateStatus('popup', 'success', '✅ 正常');
                    log('✓ Popup界面功能正常');
                } else {
                    updateStatus('popup', 'error', '❌ 错误');
                    log('✗ Popup界面功能异常');
                }
            } catch (error) {
                updateStatus('popup', 'error', '❌ 错误');
                log('✗ Popup测试失败: ' + error.message);
            }
        }
        
        async function testBackground() {
            log('测试Background脚本通信...');
            try {
                const response = await chrome.runtime.sendMessage({action: 'getStatus'});
                if (response) {
                    updateStatus('background', 'success', '✅ 正常');
                    log('✓ Background脚本通信正常');
                } else {
                    updateStatus('background', 'error', '❌ 错误');
                    log('✗ Background脚本无响应');
                }
            } catch (error) {
                updateStatus('background', 'error', '❌ 错误');
                log('✗ Background通信失败: ' + error.message);
            }
        }
        
        async function testStorage() {
            log('测试配置存储功能...');
            try {
                const testData = {testKey: 'testValue', timestamp: Date.now()};
                await chrome.storage.local.set(testData);
                const result = await chrome.storage.local.get('testKey');
                
                if (result.testKey === 'testValue') {
                    updateStatus('storage', 'success', '✅ 正常');
                    log('✓ 配置存储功能正常');
                } else {
                    updateStatus('storage', 'error', '❌ 错误');
                    log('✗ 配置存储功能异常');
                }
            } catch (error) {
                updateStatus('storage', 'error', '❌ 错误');
                log('✗ 存储测试失败: ' + error.message);
            }
        }
        
        async function testTabs() {
            log('测试标签页操作权限...');
            try {
                const tabs = await chrome.tabs.query({});
                if (tabs.length > 0) {
                    updateStatus('tabs', 'success', '✅ 正常');
                    log('✓ 标签页操作权限正常');
                } else {
                    updateStatus('tabs', 'error', '❌ 错误');
                    log('✗ 标签页操作权限异常');
                }
            } catch (error) {
                updateStatus('tabs', 'error', '❌ 错误');
                log('✗ 标签页测试失败: ' + error.message);
            }
        }
        
        async function testBilibili() {
            log('测试B站页面访问...');
            try {
                const tab = await chrome.tabs.create({
                    url: 'https://www.bilibili.com',
                    active: false
                });
                
                setTimeout(async () => {
                    try {
                        await chrome.tabs.remove(tab.id);
                        updateStatus('bilibili', 'success', '✅ 正常');
                        log('✓ B站页面访问正常');
                    } catch (error) {
                        updateStatus('bilibili', 'error', '❌ 错误');
                        log('✗ B站页面访问异常: ' + error.message);
                    }
                }, 2000);
                
            } catch (error) {
                updateStatus('bilibili', 'error', '❌ 错误');
                log('✗ B站访问测试失败: ' + error.message);
            }
        }
        
        async function testSearch() {
            log('测试视频搜索功能...');
            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'startAutoPlay',
                    config: {
                        searchKeyword: 'test',
                        playDuration: 5,
                        maxVideos: 1
                    }
                });
                
                if (response && response.success) {
                    updateStatus('search', 'success', '✅ 正常');
                    log('✓ 视频搜索功能正常');
                    
                    // 停止测试播放
                    setTimeout(async () => {
                        await chrome.runtime.sendMessage({action: 'stopAutoPlay'});
                    }, 3000);
                } else {
                    updateStatus('search', 'error', '❌ 错误');
                    log('✗ 视频搜索功能异常');
                }
            } catch (error) {
                updateStatus('search', 'error', '❌ 错误');
                log('✗ 搜索测试失败: ' + error.message);
            }
        }
        
        async function testControl() {
            log('测试控制窗口创建...');
            try {
                const controlUrl = chrome.runtime.getURL('control.html');
                const window = await chrome.windows.create({
                    url: controlUrl,
                    type: 'popup',
                    width: 400,
                    height: 300
                });
                
                setTimeout(async () => {
                    try {
                        await chrome.windows.remove(window.id);
                        updateStatus('control', 'success', '✅ 正常');
                        log('✓ 控制窗口创建正常');
                    } catch (error) {
                        updateStatus('control', 'error', '❌ 错误');
                        log('✗ 控制窗口创建异常: ' + error.message);
                    }
                }, 2000);
                
            } catch (error) {
                updateStatus('control', 'error', '❌ 错误');
                log('✗ 控制窗口测试失败: ' + error.message);
            }
        }
        
        async function testDebug() {
            log('测试调试页面访问...');
            try {
                const debugUrl = chrome.runtime.getURL('debug.html');
                const tab = await chrome.tabs.create({
                    url: debugUrl,
                    active: false
                });
                
                setTimeout(async () => {
                    try {
                        await chrome.tabs.remove(tab.id);
                        updateStatus('debug', 'success', '✅ 正常');
                        log('✓ 调试页面访问正常');
                    } catch (error) {
                        updateStatus('debug', 'error', '❌ 错误');
                        log('✗ 调试页面访问异常: ' + error.message);
                    }
                }, 2000);
                
            } catch (error) {
                updateStatus('debug', 'error', '❌ 错误');
                log('✗ 调试页面测试失败: ' + error.message);
            }
        }
        
        async function testInject() {
            log('测试脚本注入功能...');
            try {
                const tab = await chrome.tabs.create({
                    url: 'https://www.bilibili.com',
                    active: false
                });
                
                setTimeout(async () => {
                    try {
                        await chrome.scripting.executeScript({
                            target: { tabId: tab.id },
                            func: () => {
                                console.log('脚本注入测试成功');
                                return true;
                            }
                        });
                        
                        await chrome.tabs.remove(tab.id);
                        updateStatus('inject', 'success', '✅ 正常');
                        log('✓ 脚本注入功能正常');
                    } catch (error) {
                        await chrome.tabs.remove(tab.id);
                        updateStatus('inject', 'error', '❌ 错误');
                        log('✗ 脚本注入功能异常: ' + error.message);
                    }
                }, 3000);
                
            } catch (error) {
                updateStatus('inject', 'error', '❌ 错误');
                log('✗ 注入测试失败: ' + error.message);
            }
        }
        
        async function runAllTests() {
            log('=== 开始运行所有测试 ===');
            
            const tests = [
                testPopup,
                testBackground,
                testStorage,
                testTabs,
                testBilibili,
                testSearch,
                testControl,
                testDebug,
                testInject
            ];
            
            for (let i = 0; i < tests.length; i++) {
                await tests[i]();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            log('=== 所有测试完成 ===');
        }
        
        log('功能测试页面已加载，可以开始测试');
    </script>
</body>
</html>
