<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .config-section {
      background: rgba(255,255,255,0.1);
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 15px;
      backdrop-filter: blur(10px);
    }
    
    .config-item {
      margin-bottom: 12px;
    }
    
    .config-item label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      font-size: 14px;
    }
    
    .config-item input, .config-item select {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 5px;
      background: rgba(255,255,255,0.9);
      color: #333;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }
    
    button {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
    }
    
    .start-btn {
      background: #4CAF50;
      color: white;
    }
    
    .start-btn:hover {
      background: #45a049;
      transform: translateY(-2px);
    }
    
    .stop-btn {
      background: #f44336;
      color: white;
    }
    
    .stop-btn:hover {
      background: #da190b;
      transform: translateY(-2px);
    }
    
    .status {
      text-align: center;
      margin-top: 15px;
      padding: 10px;
      background: rgba(255,255,255,0.1);
      border-radius: 8px;
      font-size: 14px;
    }
    
    .progress {
      margin-top: 10px;
    }
    
    .progress-bar {
      width: 100%;
      height: 6px;
      background: rgba(255,255,255,0.3);
      border-radius: 3px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background: #4CAF50;
      width: 0%;
      transition: width 0.3s ease;
    }
    
    .video-list {
      max-height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }
    
    .video-item {
      padding: 8px;
      margin: 5px 0;
      background: rgba(255,255,255,0.1);
      border-radius: 5px;
      font-size: 12px;
    }
    
    .video-item.completed {
      background: rgba(76, 175, 80, 0.3);
    }
    
    .video-item.playing {
      background: rgba(255, 193, 7, 0.3);
    }

    .keep-open-notice {
      background: rgba(255, 255, 255, 0.1);
      padding: 10px;
      border-radius: 5px;
      margin-top: 10px;
      font-size: 12px;
      text-align: center;
      border: 1px dashed rgba(255, 255, 255, 0.3);
    }

    .running-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      background: #4CAF50;
      border-radius: 50%;
      margin-right: 5px;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎬 B站自动播放器</h1>
  </div>
  
  <div class="config-section">
    <div class="config-item">
      <label for="searchKeyword">搜索关键词:</label>
      <input type="text" id="searchKeyword" value="mcp技术" placeholder="输入搜索关键词">
    </div>
    
    <div class="config-item">
      <label for="playDuration">播放时长(秒):</label>
      <input type="number" id="playDuration" value="30" min="10" max="300">
    </div>
    
    <div class="config-item">
      <label for="maxVideos">最大播放数量:</label>
      <input type="number" id="maxVideos" value="5" min="1" max="20">
    </div>
  </div>
  
  <div class="button-group">
    <button id="startBtn" class="start-btn">开始播放</button>
    <button id="stopBtn" class="stop-btn">停止播放</button>
  </div>
  
  <div class="status" id="status">
    准备就绪
  </div>
  
  <div class="progress">
    <div class="progress-bar">
      <div class="progress-fill" id="progressFill"></div>
    </div>
  </div>
  
  <div class="video-list" id="videoList"></div>

  <div class="keep-open-notice" id="keepOpenNotice" style="display: none;">
    <span class="running-indicator"></span>
    插件正在运行中，请保持此窗口打开
  </div>

  <script src="popup.js"></script>
</body>
</html>
