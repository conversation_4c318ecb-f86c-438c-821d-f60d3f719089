# 安装指南

## 快速开始

### 1. 生成图标文件

插件需要PNG格式的图标文件。请按以下步骤生成：

#### 方法一：使用浏览器生成（推荐）
1. 双击打开 `create_icons.html` 文件
2. 浏览器会显示4个不同尺寸的图标
3. 右键点击每个图标，选择"图片另存为"
4. 分别保存为：
   - `icon16.png` (16x16像素)
   - `icon32.png` (32x32像素)  
   - `icon48.png` (48x48像素)
   - `icon128.png` (128x128像素)
5. 将这4个PNG文件放入 `icons/` 目录

#### 方法二：使用在线工具
1. 打开 `icons/` 目录中的SVG文件
2. 使用在线SVG转PNG工具（如 convertio.co）
3. 转换为对应尺寸的PNG文件
4. 保存到 `icons/` 目录

### 2. 安装插件

1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择插件所在的文件夹
6. 插件安装成功后会出现在扩展列表中

### 3. 使用插件

1. 点击浏览器工具栏中的插件图标
2. 在弹出窗口中配置参数：
   - **搜索关键词**：要搜索的内容（默认：mcp技术）
   - **播放时长**：每个视频播放秒数（默认：30秒）
   - **最大播放数量**：最多播放几个视频（默认：5个）
3. 点击"开始播放"按钮
4. 插件会自动打开B站并开始播放视频

## 故障排除

### 插件无法加载
- 检查是否开启了开发者模式
- 确认所有文件完整，特别是图标文件
- 查看扩展页面的错误信息

### 搜索无结果
- 检查网络连接
- 尝试更换搜索关键词
- 确认B站页面正常加载

### 视频无法播放
- 检查视频是否需要登录
- 尝试手动播放测试
- 查看浏览器控制台错误

## 文件结构

```
bilibili-auto-player/
├── manifest.json          # 插件配置文件
├── popup.html            # 弹窗界面
├── popup.js              # 弹窗逻辑
├── background.js         # 后台服务脚本
├── content.js            # 内容脚本
├── icons/                # 图标文件夹
│   ├── icon16.png       # 需要生成
│   ├── icon32.png       # 需要生成
│   ├── icon48.png       # 需要生成
│   ├── icon128.png      # 需要生成
│   ├── icon16.svg       # SVG源文件
│   ├── icon32.svg       # SVG源文件
│   ├── icon48.svg       # SVG源文件
│   └── icon128.svg      # SVG源文件
├── create_icons.html     # 图标生成工具
├── test.html            # 测试页面
├── README.md            # 详细说明
└── INSTALL.md           # 本安装指南
```

## 注意事项

1. **网络环境**：确保能正常访问B站
2. **浏览器权限**：插件需要访问B站页面的权限
3. **页面加载**：等待页面完全加载后再开始操作
4. **播放限制**：某些视频可能因版权等原因无法播放
5. **资源占用**：长时间运行可能占用较多系统资源

## 更新插件

如果修改了插件代码，需要重新加载：

1. 访问 `chrome://extensions/`
2. 找到插件，点击刷新按钮
3. 或者先移除插件，再重新加载

## 卸载插件

1. 访问 `chrome://extensions/`
2. 找到插件，点击"移除"按钮
3. 确认删除

## 技术支持

如果遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查插件是否有更新
3. 尝试重新安装插件
4. 查看README.md中的详细说明
